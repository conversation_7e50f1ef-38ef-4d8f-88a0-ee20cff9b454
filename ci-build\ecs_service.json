{"containerDefinitions": [{"name": "#-{container_name}-#", "mountPoints": [], "image": "#{aws_account_id}#.dkr.ecr.#{aws_region}#.amazonaws.com/#-{container_name}-#:#-{BUILD_BUILDNUMBER}-#", "portMappings": [{"protocol": "tcp", "containerPort": 80, "hostPort": 80}], "environment": [{"name": "Logging__Console__DisableColors", "value": "true"}, {"name": "FRONTEND_HOST", "value": "127.0.0.1"}, {"name": "IMAGEPROXY_HOST", "value": "127.0.0.1"}, {"name": "SERVICE_NAME", "value": "widget"}], "secrets": [{"name": "VITE_WORDPRESS_NEWS_URL", "valueFrom": "/ENT/Widget/VITE_WORDPRESS_NEWS_URL"}, {"name": "VITE_AZURE_CLIENT_ID", "valueFrom": "/ENT/Widget/VITE_AZURE_CLIENT_ID"}, {"name": "VITE_AZURE_AUTHORITY", "valueFrom": "/ENT/Widget/VITE_AZURE_AUTHORITY"}, {"name": "VITE_AZURE_KNOWN_AUTHORITY", "valueFrom": "/ENT/Widget/VITE_AZURE_KNOWN_AUTHORITY"}, {"name": "VITE_PUBLIC_SHOPIFY_DOMAIN", "valueFrom": "/ENT/Widget/VITE_PUBLIC_SHOPIFY_DOMAIN"}, {"name": "VITE_PUBLIC_SHOPIFY_ACCESS_TOKEN", "valueFrom": "/ENT/Widget/VITE_PUBLIC_SHOPIFY_ACCESS_TOKEN"}, {"name": "VITE_PUBLIC_SHOPIFY_STORE_DOMAIN", "valueFrom": "/ENT/Widget/VITE_PUBLIC_SHOPIFY_STORE_DOMAIN"}, {"name": "VITE_PUBLIC_STOREFRONT_ACCESS_TOKEN", "valueFrom": "/ENT/Widget/VITE_PUBLIC_STOREFRONT_ACCESS_TOKEN"}, {"name": "VITE_LOYALTY_MATCH_API_URL", "valueFrom": "/ENT/Widget/VITE_LOYALTY_MATCH_API_URL"}, {"name": "VITE_LOYALTY_MATCH_API_KEY", "valueFrom": "/ENT/Widget/VITE_LOYALTY_MATCH_API_KEY"}, {"name": "LOYALTY_PROXY_NAMESPACE", "valueFrom": "/ENT/LOYALTY/LOYALTY_PROXY_NAMESPACE"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-region": "#{aws_region}#", "awslogs-stream-prefix": "ecs", "awslogs-group": "/ecs/#-{container_name}-#"}}, "essential": true, "volumesFrom": []}, {"name": "imgproxy", "image": "darthsim/imgproxy:v3.16.0", "essential": true, "portMappings": [{"protocol": "tcp", "containerPort": 8080, "hostPort": 8080}], "environment": [{"name": "IMGPROXY_PNG_INTERLACED", "value": "true"}, {"name": "IMGPROXY_PNG_QUANTIZE", "value": "true"}, {"name": "IMGPROXY_PNG_QUANTIZATION_COLORS", "value": "256"}, {"name": "IMGPROXY_CONCURRENCY", "value": "1"}, {"name": "IMGPROXY_DOWNLOAD_TIMEOUT", "value": "15"}, {"name": "IMGPROXY_QUALITY", "value": "70"}, {"name": "IMGPROXY_ALLOW_LOOPBACK_SOURCE_ADDRESSES", "value": "true"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/#-{container_name}-#", "awslogs-region": "#{aws_region}#", "awslogs-stream-prefix": "ecs"}}}, {"name": "loyaltyproxy", "image": "#{aws_account_id}#.dkr.ecr.#{aws_region}#.amazonaws.com/loyaltyproxy:#-{BUILD_BUILDNUMBER}-#", "essential": true, "portMappings": [{"protocol": "tcp", "containerPort": 3000, "hostPort": 3000}], "secrets": [{"name": "LOYALTY_MATCH_API_URL", "valueFrom": "/ENT/LOYALTY/LOYALTY_MATCH_API_URL"}, {"name": "LOYALTY_MATCH_KEY", "valueFrom": "/ENT/LOYALTY/LOYALTY_MATCH_KEY"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/#-{container_name}-#", "awslogs-region": "#{aws_region}#", "awslogs-stream-prefix": "ecs"}}}], "family": "#-{container_name}-#", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "#{container_cpu}#", "memory": "#{container_memory}#"}