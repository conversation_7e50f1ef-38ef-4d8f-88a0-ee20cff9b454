{"name": "loyalty-proxy", "version": "1.0.0", "type": "module", "scripts": {"start": "node --loader ts-node/esm src/server.ts", "dev": "nodemon --watch src -e ts --exec node --loader ts-node/esm src/server.ts", "build": "tsc"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}