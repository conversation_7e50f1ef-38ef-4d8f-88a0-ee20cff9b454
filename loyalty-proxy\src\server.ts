import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import path from 'path'

// Load environment variables
dotenv.config()

const app = express()
const LOYALTY_MATCH_API = process.env.LOYALTY_MATCH_API_URL
const LOYALTY_MATCH_KEY = process.env.LOYALTY_MATCH_KEY

// Debug logs
console.log('Environment variables loaded:', {
  API_URL: LOYALTY_MATCH_API,
  KEY_EXISTS: !!LOYALTY_MATCH_KEY,
})

app.use(
  cors({
    origin: function (origin, callback) {
      console.log('Incoming Origin:', origin)

      const allowed = [
        'http://localhost:5173',
        'https://bonniewidget.wpenginepowered.com',
        'https://widget.oc-club.community',
        'https://widget.uat.oc-club.community',
        'https://widget.dev.oc-club.community',
      ]

      const isAllowed = !origin || allowed.includes(origin)

      if (isAllowed) {
        return callback(null, true)
      }

      console.warn('CORS rejected origin:', origin)
      return callback(new Error('Not allowed by CORS'))
    },
    credentials: true,
  }),
)
app.use(express.json())

// Test endpoint
app.get('/api/loyalty/test', (req, res) => {
  res.json({ status: 'ok', message: 'Proxy server is running' })
})

// Health check endpoint for load balancers
app.get('/api/loyalty/health', (req, res) => {
  res.status(200).send('OK')
})

// Handle registration
app.put('/api/loyalty/register', async (req, res) => {
  try {
    const { userId, email } = req.body

    // Log full request as received by proxy
    console.log('Registration proxy request received:', {
      path: req.path,
      method: req.method,
      headers: req.headers,
      body: req.body,
      userId: userId,
      email: email,
    })

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    // Use emailAddress instead of email for LoyaltyMatch
    const requestBody = {
      userId,
      emailAddress: email, // Changed from email to emailAddress
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/registration?${params}`

    // Log full request details being sent to LoyaltyMatch
    console.log('Sending to LoyaltyMatch API:', {
      url: url,
      method: 'PUT',
      body: requestBody,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    })

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    const responseText = await response.text()
    console.log('LoyaltyMatch response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    const data = JSON.parse(responseText)
    res.json(data)
  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({
      error: 'Registration failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Update your current points endpoint to properly include userId in path
app.get('/api/loyalty/currentpoints/userId/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    // Fix the URL structure to match what the API expects
    const url = `${LOYALTY_MATCH_API}/api/currentpoints/userId/${userId}?${params}`
    console.log('Getting points for user:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('Points response text:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const data = JSON.parse(responseText)
      res.json(data)
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('Points fetch error:', error)
    res.status(500).json({
      error: 'Failed to fetch points',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Award points endpoint
app.put('/api/loyalty/memberpointsactivity', async (req, res) => {
  try {
    const pointsData = req.body

    // Log the merchant ID if present (like the Hub does)
    if (pointsData.merchantId) {
      console.log(
        'Processing incentive with merchantId:',
        pointsData.merchantId,
      )
    }

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/memberpointsactivity?${params}`
    console.log('Awarding points:', url, pointsData)

    // Forward the request with the merchant ID included in the body (like the Hub does)
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pointsData), // This now includes merchantId
    })

    const responseText = await response.text()
    console.log('Points award response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const data = JSON.parse(responseText)
      res.json(data)
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('Points award error:', error)
    res.status(500).json({
      error: 'Failed to award points',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Get members endpoint
app.get('/api/loyalty/members', async (req, res) => {
  try {
    const { userId } = req.query // Get userId from query params

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
      userId: userId as string, // Add userId to params
    })

    const url = `${LOYALTY_MATCH_API}/api/SearchMembers?${params}`
    console.log('Fetching members with userId filter:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('Members response text:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const data = JSON.parse(responseText)
      console.log('Parsed members data:', data)
      res.json(data)
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('Members fetch error:', error)
    res.status(500).json({
      error: 'Failed to fetch members',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Get badges endpoint
app.get('/api/loyalty/badges', async (req, res) => {
  try {
    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/badges?${params}`
    console.log('Fetching badges:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('Badges raw response:', responseText) // Log the raw response to see the structure

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const data = JSON.parse(responseText)
      res.json(data)
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('Badges fetch error:', error)
    res.status(500).json({
      error: 'Failed to fetch badges',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Get user badges endpoint
app.get('/api/loyalty/badgesbymember/userId/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/badgesbymember/userId/${userId}?${params}`
    console.log('Fetching user badges:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('User badges response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const data = JSON.parse(responseText)
      res.json(data)
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('User badges fetch error:', error)
    res.status(500).json({
      error: 'Failed to fetch user badges',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Award badge endpoint
app.put(
  '/api/loyalty/addbadgeactivity/:badgeId/userId/:userId',
  async (req, res) => {
    try {
      const { badgeId, userId } = req.params
      const badgeData = req.body

      if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
        throw new Error('Missing required environment variables')
      }

      const params = new URLSearchParams({
        format: 'json',
        odpass: LOYALTY_MATCH_KEY,
      })

      const url = `${LOYALTY_MATCH_API}/api/addbadgeactivity/${badgeId}/userId/${userId}?${params}`
      console.log('Awarding badge:', url, badgeData)

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(badgeData),
      })

      const responseText = await response.text()
      console.log('Badge award response:', responseText)

      if (!response.ok) {
        throw new Error(`LoyaltyMatch error: ${responseText}`)
      }

      try {
        const data = JSON.parse(responseText)
        res.json(data)
      } catch (e) {
        throw new Error(`Invalid JSON response: ${responseText}`)
      }
    } catch (error) {
      console.error('Badge award error:', error)
      res.status(500).json({
        error: 'Failed to award badge',
        details: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  },
)

// Get points activity history endpoint
app.get(
  '/api/loyalty/memberpointsactivities/userId/:userId',
  async (req, res) => {
    try {
      const { userId } = req.params

      if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
        throw new Error('Missing required environment variables')
      }

      const params = new URLSearchParams({
        format: 'json',
        odpass: LOYALTY_MATCH_KEY,
      })

      // Use the exact URL structure that works in the browser
      const url = `${LOYALTY_MATCH_API}/api/memberpointsactivities/userId/${userId}?${params}`
      console.log('Fetching points activities from:', url)

      const response = await fetch(url)
      const responseText = await response.text()
      console.log('Points activities raw response:', responseText)

      if (!response.ok) {
        throw new Error(`LoyaltyMatch error: ${responseText}`)
      }

      try {
        const data = JSON.parse(responseText)
        console.log(
          'Points activities parsed data structure:',
          Object.keys(data),
        )
        res.json(data)
      } catch (e) {
        throw new Error(`Invalid JSON response: ${responseText}`)
      }
    } catch (error) {
      console.error('Points activities fetch error:', error)
      res.status(500).json({
        error: 'Failed to fetch points activities',
        details: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  },
)

// Get all available incentives - enhanced logging
app.get('/api/loyalty/incentives', async (req, res) => {
  try {
    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/incentives?${params}`
    console.log('Fetching incentives from:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('Raw incentives response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      const rawData = JSON.parse(responseText)

      // Check if the response is already an array
      if (Array.isArray(rawData)) {
        console.log('Received array of incentives:', rawData.length)
        // Wrap the array in the expected format
        res.json({ apiIncentives: rawData })
      } else {
        console.log('Incentives data structure:', Object.keys(rawData))

        // Create a fallback array of sample incentives if no incentives are found
        if (!rawData.apiIncentives || rawData.apiIncentives.length === 0) {
          console.log('No incentives found, providing sample incentives')
          rawData.apiIncentives = [
            // Your sample incentives
          ]
        }

        res.json(rawData)
      }
    } catch (e) {
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  } catch (error) {
    console.error('Incentives fetch error:', error)
    res.status(500).json({
      error: 'Failed to fetch incentives',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Award points using incentive
app.put(
  '/api/loyalty/awardIncentive/:userId/:incentiveId',
  async (req, res) => {
    try {
      const { userId, incentiveId } = req.params

      if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
        throw new Error('Missing required environment variables')
      }

      const params = new URLSearchParams({
        format: 'json',
        odpass: LOYALTY_MATCH_KEY,
      })

      const url = `${LOYALTY_MATCH_API}/api/memberpointsactivity/userId/${userId}/target/${incentiveId}?${params}`
      console.log('Awarding incentive:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      })

      const responseText = await response.text()
      console.log('Incentive award response:', responseText)

      if (!response.ok) {
        throw new Error(`LoyaltyMatch error: ${responseText}`)
      }

      try {
        // LoyaltyMatch might return empty response for successful calls
        const data = responseText.trim()
          ? JSON.parse(responseText)
          : { success: true }
        res.json(data)
      } catch (e) {
        // Handle non-JSON response
        res.json({ success: true, message: 'Incentive awarded successfully' })
      }
    } catch (error) {
      console.error('Incentive award error:', error)
      res.status(500).json({
        error: 'Failed to award incentive',
        details: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  },
)

app.put(
  '/api/loyalty/memberpointsactivity/userId/:userId/target/:incentiveId',
  async (req, res) => {
    try {
      const { userId, incentiveId } = req.params

      if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
        throw new Error('Missing required environment variables')
      }

      const params = new URLSearchParams({
        format: 'json',
        odpass: LOYALTY_MATCH_KEY,
      })

      const url = `${LOYALTY_MATCH_API}/api/memberpointsactivity/userId/${userId}/target/${incentiveId}?${params}`
      console.log('Awarding incentive:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      })

      const responseText = await response.text()
      console.log('Incentive award response:', responseText)

      if (!response.ok) {
        throw new Error(`LoyaltyMatch error: ${responseText}`)
      }

      try {
        // LoyaltyMatch might return empty response for successful calls
        const data = responseText.trim()
          ? JSON.parse(responseText)
          : { success: true }
        res.json(data)
      } catch (e) {
        // Handle non-JSON response
        res.json({ success: true, message: 'Incentive awarded successfully' })
      }
    } catch (error) {
      console.error('Incentive award error:', error)
      res.status(500).json({
        error: 'Failed to award incentive',
        details: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  },
)

// Badge reconciliation endpoint
app.get('/api/loyalty/badgereconciliation/userId/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/badgereconciliation/userId/${userId}?${params}`
    console.log('Running badge reconciliation:', url)

    const response = await fetch(url)
    const responseText = await response.text()
    console.log('Badge reconciliation response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    try {
      // LoyaltyMatch might return empty response for successful calls
      const data = responseText.trim()
        ? JSON.parse(responseText)
        : { success: true }
      res.json(data)
    } catch (e) {
      // Handle non-JSON response
      res.json({
        success: true,
        message: 'Badge reconciliation completed successfully',
      })
    }
  } catch (error) {
    console.error('Badge reconciliation error:', error)
    res.status(500).json({
      error: 'Failed to run badge reconciliation',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

// Handle member update endpoint
app.put('/api/loyalty/updatemember', async (req, res) => {
  try {
    const { userId, apiMemberAccount } = req.body

    // Log full request as received by proxy
    console.log('Member update request received:', {
      path: req.path,
      method: req.method,
      headers: req.headers,
      body: req.body,
      userId,
      apiMemberAccount,
      hasFacebookData: apiMemberAccount?.facebookHandle ? true : false,
      socialFields: Object.keys(apiMemberAccount || {}).filter((key) =>
        ['twitterHandle', 'facebookHandle', 'instagramHandle'].includes(key),
      ),
    })

    if (!LOYALTY_MATCH_API || !LOYALTY_MATCH_KEY) {
      throw new Error('Missing required environment variables')
    }

    const params = new URLSearchParams({
      format: 'json',
      odpass: LOYALTY_MATCH_KEY,
    })

    const url = `${LOYALTY_MATCH_API}/api/updatemember?${params}`

    // Create the request body matching the format expected by LoyaltyMatch
    const requestBody = {
      userId,
      apiMemberAccount,
    }

    console.log('Sending to LoyaltyMatch API:', {
      url,
      method: 'PUT',
      body: requestBody,
    })

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    const responseText = await response.text()
    console.log('LoyaltyMatch update member response:', responseText)

    if (!response.ok) {
      throw new Error(`LoyaltyMatch error: ${responseText}`)
    }

    // Handle empty or non-JSON responses gracefully
    try {
      const data = responseText.trim()
        ? JSON.parse(responseText)
        : { success: true }
      res.json(data)
    } catch (e) {
      console.warn(
        'Response is not JSON but request was successful:',
        responseText,
      )
      res.json({ success: true, message: 'Member updated successfully' })
    }
  } catch (error) {
    console.error('Member update error:', error)
    res.status(500).json({
      error: 'Member update failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
})

const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`Proxy server running on port ${PORT}`)
  console.log('API URL:', LOYALTY_MATCH_API)
})
