{"name": "occ2-widget", "version": "0.1.58", "private": false, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "style": "./dist/index.css", "exports": {".": {"types": "./dist/index.d.ts", "style": "./dist/index.css", "import": "./dist/index.js", "default": "./dist/index.js"}, "./style": "./dist/index.css", "./dist/index.css": "./dist/index.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:plugin": "vite build --config vite.plugin.config.ts", "lint": "eslint .", "preview": "vite preview", "host": "vite --host"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "dependencies": {"@apollo/client": "^3.13.1", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/credential-provider-node": "^3.787.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^7.28.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@shopify/storefront-api-client": "^1.0.5", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@types/crypto-js": "^4.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.7", "json-canonicalize": "^2.0.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.474.0", "react-day-picker": "^9.6.7", "react-hook-form": "^7.54.2", "react-use-websocket": "^4.13.0", "shaka-player": "^4.13.6", "side-chat": "github:AndrewTi/side-chat", "swr": "^2.3.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.13.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sass-embedded": "^1.83.4", "tailwindcss": "3.4.1", "terser": "^5.39.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-plugin-dts": "^3.7.3"}}