import { useEffect, useState } from 'react'
import { ApolloProvider } from '@apollo/client'
import { ThemeProvider } from './components/theme-provider'
import { UserProvider } from './context/UserContext'
import { AuthProvider } from './context/AuthContext'
import { AppProvider } from './context/AppContext'
import { CartProvider } from './context/CartContext'
import { LoyaltyProvider } from './context/LoyaltyContext'
import { IncentiveTrackerProvider } from './context/IncentiveTrackerContext'
import { ShopProvider } from './context/ShopContext'
import { client } from './lib/apollo'

import { ModalProvider } from './context/ModalContext'
import { TokenRefresher } from './components/TokenRefresher'
import { CollectionsType } from './types/app'
import { AppContent } from './AppContent'
import { isDevelopment, FALLBACK_ARTIST_ID } from './constants'

import './App.css'

interface AppProps {
  isSidebarOpen: boolean
  toggleSidebar: () => void
  artistId?: string
}

const App = ({ isSidebarOpen, toggleSidebar, artistId }: AppProps) => {
  const [artistIdFromConfig, setArtistIdFromConfig] = useState<string>()
  const [isLoading, setIsLoading] = useState(isDevelopment)

  useEffect(() => {
    if (!isDevelopment) return

    const fetchArtistId = async () => {
      try {
        const response = await fetch(
          'https://cdn.oc-club.community/api/v6/catalog/f7bc2621-221e-450a-b8b6-124f032170b3/entity.json',
        )

        if (!response.ok) throw new Error('Network response was not ok')

        const data: CollectionsType = await response.json()

        const contentId = data?.Properties?.find(
          (prop) => prop.Name === 'Content:Id',
        )?.Value

        setArtistIdFromConfig(contentId || FALLBACK_ARTIST_ID)
      } catch (error) {
        console.error('Failed to fetch artist ID:', error)
        setArtistIdFromConfig(FALLBACK_ARTIST_ID)
      } finally {
        setIsLoading(false)
      }
    }

    fetchArtistId()
  }, [])

  const resolvedArtistId = isDevelopment
    ? artistIdFromConfig || FALLBACK_ARTIST_ID
    : artistId || FALLBACK_ARTIST_ID

  return (
    <ApolloProvider client={client}>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <AppProvider>
          <ShopProvider>
            <CartProvider>
              <UserProvider>
                <AuthProvider>
                  <LoyaltyProvider>
                    <IncentiveTrackerProvider>
                      <ModalProvider>
                        <TokenRefresher />
                        <AppContent
                          isSidebarOpen={isSidebarOpen}
                          artistId={resolvedArtistId}
                          toggleSidebar={toggleSidebar}
                          isLoading={isLoading}
                        />
                      </ModalProvider>
                    </IncentiveTrackerProvider>
                  </LoyaltyProvider>
                </AuthProvider>
              </UserProvider>
            </CartProvider>
          </ShopProvider>
        </AppProvider>
      </ThemeProvider>
    </ApolloProvider>
  )
}

export default App
