import { useEffect } from 'react'
import Sidebar from './components/Sidebar/Sidebar'
import useAppEndpoints from './hooks/useAppEndpoints'
import useLocalization from './hooks/useLocalization'
import { useAppStore } from './store'
import { setStorageLocales } from './helpers/storage'
import { getArtistConfig } from './lib/artistConfig'
import { useAppContext } from './context/AppContext'
import { isDevelopment } from './constants'
import { ArtistColorsConfig } from './types/artist'

import './App.css'

interface AppContentProps {
  isSidebarOpen: boolean
  toggleSidebar: () => void
  isLoading: boolean
  artistId?: string
}

export const AppContent = ({
  isSidebarOpen,
  toggleSidebar,
  isLoading,
  artistId,
}: AppContentProps) => {
  const { isLoading: endpointsLoading, data: endpointsData } = useAppEndpoints()
  const { setConfig, setArtistId, formattedFeaturedArtistData, setColorsInfo } =
    useAppContext()

  useEffect(() => {
    const handleAsyncArtist = async () => {
      if (artistId) {
        const config = await getArtistConfig(
          artistId,
          formattedFeaturedArtistData,
        )
        setConfig(config)
        console.log('config: ', config)

        if (config?.colors) {
          handleConfigColors(config?.colors)
        }
      }
    }

    handleAsyncArtist()
  }, [artistId, formattedFeaturedArtistData])

  const handleConfigColors = async (url: string) => {
    try {
      const response = await fetch(url)
      const responseData = await response.json()

      const colorsProperties = responseData?.Properties?.filter(
        (property: any) => property?.Name?.startsWith('Carousel:Design:'),
      )

      const nameMapping: Record<string, string> = {
        'Carousel:Design:Light:Background:Color': 'hoverColor',
        'Carousel:Design:Light:FontColor:Main': 'backgroundColor',
        'Carousel:Design:Light:FontColor:ShortInfo': 'secondaryBackgroundColor',
        'Carousel:Design:Light:secondaryText': 'secondaryText',
        'Carousel:Design:Light:mainText': 'fontColor',
        'Carousel:Design:Light:Font': 'font',
        'Carousel:Design:Light:Logo': 'logo',
      }

      const transformedColors = colorsProperties.reduce(
        (acc: any, curr: any) => {
          const key = nameMapping[curr.Name]
          if (key) {
            acc[key] = curr.Value
          }
          return acc
        },
        {},
      )

      setColorsInfo(transformedColors as ArtistColorsConfig)
    } catch (error) {
      console.error('Error for config.menu', error)
    }
  }

  const { isLoading: localesLoading, data: localesData } = useLocalization()

  useEffect(() => {
    setArtistId(artistId ?? null)
    const { setLocales } = useAppStore.getState()
    if (!localesLoading && localesData) {
      // Set new expiry FIRST
      localStorage.setItem('LocalesExpiry', String(Date.now() + 3600000)) // +1 hour

      // Store new data
      setStorageLocales(localesData)
      setLocales(localesData)

      // Only now is it safe - old data has been replaced
    }
  }, [localesLoading, localesData])

  useEffect(() => {
    const { setEndpoints, endpoints } = useAppStore.getState()
    if (!endpointsLoading && endpointsData && !endpoints) {
      setEndpoints(endpointsData)
    }
  }, [endpointsLoading, endpointsData])

  if (endpointsLoading || localesLoading || isLoading) {
    return null
  }

  return (
    <div className={isDevelopment ? 'flex h-screen relative' : 'contents'}>
      {isDevelopment && (
        <main className="h-full w-full bg-black">
          <img
            src="/images/main-page-placeholder.png"
            alt="main page placeholder"
            className="object-contain"
            height="100%"
            width="100%"
          />
        </main>
      )}
      {!isSidebarOpen && (
        <button
          className="bg-accent text-primary-foreground fixed top-1/2 right-0 transform -translate-y-1/2 w-10 h-48 min-h-[198px] border-none cursor-pointer flex items-center justify-center z-1000 p-4 transition-opacity duration-300 ease-in-out rounded-md"
          onClick={toggleSidebar}
        >
          <span className="transform -rotate-90 whitespace-nowrap text-sm font-bold tracking-widest text-primary-foreground">
            Community
          </span>
        </button>
      )}
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
    </div>
  )
}
