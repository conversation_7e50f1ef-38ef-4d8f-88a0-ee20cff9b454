import { jwtDecode } from 'jwt-decode'
import { getDeviceData } from '@/helpers/deviceData'
import { getLanguage, uuidv4 } from '@/helpers'
import { AuthSocialData, AuthParsedCredentials } from '@/types/auth'
import { CollectionsType } from '@/types/app'
import { UserData } from '@/types/user'
import getBrowserType from '@/helpers/getBrowserType'
import { fetcher } from '.'

const API_BASE_URL = 'https://bgj.oc-club.community/Interop'

export const contentAuthorize = (
  contentId: string,
): Promise<CollectionsType> => {
  const url = 'https://odw.oc-club.community/api/v1.0/Authorize'

  const searchParams = new URLSearchParams(location.search)
  const seriesId = searchParams.get('seriesId') || ''
  const seasonId = searchParams.get('seasonId') || ''
  const drmType = getBrowserType() === 'Safari' ? 'HLS' : 'DASH'

  const finalPayload = []

  finalPayload.push(
    {
      Value: contentId,
      CollectionName: 'Post',
      Name: 'Content:Id',
    },
    { CollectionName: 'Post', Name: 'Content:Formats', Value: drmType },
    {
      Value: seriesId,
      CollectionName: 'Post',
      Name: 'Content:Parent:Parent:Id',
    },
    {
      Value: seasonId,
      CollectionName: 'Post',
      Name: 'Content:Parent:Id',
    },
  )

  return fetcher<CollectionsType>(url, {
    method: 'post',
    body: JSON.stringify(finalPayload),
  })
}

export const authenticateUser = async (
  userData: AuthSocialData,
): Promise<CollectionsType> => {
  const provider = localStorage.getItem('provider') as
    | 'google'
    | 'apple'
    | 'facebook'

  let accessToken = ''
  let picture = ''
  let email = ''
  let name = ''
  let sub = ''

  if (provider === 'facebook') {
    const data = JSON.parse(userData.credential)
    picture = data?.picture
    email = data?.email
    name = data?.name
    sub = data?.sub
    accessToken = data?.accessToken
  } else {
    const data: AuthParsedCredentials = jwtDecode(userData.credential)
    accessToken = userData?.credential
    picture = data?.picture
    email = data?.email
    name = data?.name
    sub = data?.sub
  }

  let deviceId = localStorage.getItem('Custom:Device:UniqueId')
  if (!deviceId) {
    deviceId = uuidv4()
    localStorage.setItem('Custom:Device:UniqueId', deviceId)
  }
  const { deviceName, deviceSubType, deviceType, deviceModel } = getDeviceData()

  const requestBody = [
    {
      Name: 'Custom:Device:UniqueId',
      Value: deviceId,
    },
    {
      Name: 'Custom:User:Name',
      Value: name,
    },
    {
      Name: 'Social:Auth:Token',
      Value: accessToken,
    },
    {
      Name: 'Social:User:Id',
      Value: sub,
    },
    {
      Name: 'Social:Auth:Provider',
      Value: provider,
    },
    {
      Name: 'Custom:User:Avatar',
      Value: picture,
    },
    {
      Name: 'Custom:Device:Type',
      Value: deviceType,
    },
    {
      Name: 'Custom:Device:Name',
      Value: deviceName,
    },
    {
      Name: 'Custom:Device:SubType',
      Value: deviceSubType,
    },
    {
      Name: 'Custom:Device:Model',
      Value: `${deviceModel}`,
    },
    {
      Name: 'Custom:Device:Locale',
      Value: getLanguage(),
    },
    {
      Name: 'Custom:User:Email',
      Value: email,
    },
  ]

  const response = await fetch(`${API_BASE_URL}/Authenticate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  return await response.json()
}

export const registerUserAction = async (
  userData: UserData & AuthSocialData,
): Promise<CollectionsType> => {
  const provider = localStorage.getItem('provider') as
    | 'facebook'
    | 'google'
    | 'apple'

  let accessToken = ''
  let sub = ''
  if (provider === 'facebook') {
    const data = JSON.parse(userData.credential)
    accessToken = data?.accessToken
    sub = data?.sub
  } else {
    const data: AuthParsedCredentials = jwtDecode(userData.credential)
    accessToken = userData?.credential
    sub = data?.sub
  }

  const todayISOString = new Date().toISOString()
  let deviceId = localStorage.getItem('Custom:Device:UniqueId')
  if (!deviceId) {
    deviceId = uuidv4()
    localStorage.setItem('Custom:Device:UniqueId', deviceId)
  }
  const { deviceName, deviceSubType, deviceType, deviceModel } = getDeviceData()

  const payload = [
    {
      Name: 'Custom:User:Type',
      Value: 'Creator',
    },
    {
      Name: 'Custom:License:Accept:Date',
      Value: todayISOString,
    },
    {
      Name: 'Custom:Policy:Accept:Date',
      Value: todayISOString,
    },

    {
      Name: 'Custom:Device:UniqueId',
      Value: deviceId,
    },
    {
      Name: 'Custom:User:Name',
      Value: userData['Custom:User:Name'],
    },
    {
      Name: 'Custom:User:Nickname',
      Value: userData['Custom:User:Nickname'],
    },
    {
      Name: 'Customer:Location:City',
      Value: userData['Customer:Location:City'],
    },
    {
      Name: 'Customer:Location:Country',
      Value: userData['Customer:Location:Country'],
    },
    {
      Name: 'Social:Auth:Token',
      Value: accessToken,
    },
    {
      Name: 'Social:User:Id',
      Value: sub,
    },
    {
      Name: 'Social:Auth:Provider',
      Value: provider,
    },
    {
      Name: 'Custom:User:Avatar',
      Value: userData['Custom:User:Avatar'],
    },
    {
      Name: 'Custom:Device:Type',
      Value: deviceType,
    },
    {
      Name: 'Custom:Device:Name',
      Value: deviceName,
    },
    {
      Name: 'Custom:Device:SubType',
      Value: deviceSubType,
    },
    {
      Name: 'Custom:Device:Model',
      Value: `${deviceModel}`,
    },
    {
      Name: 'Custom:Device:Locale',
      Value: getLanguage(),
    },
    {
      Name: 'Custom:User:Email',
      Value: userData['Custom:User:Email'],
    },
    {
      Name: 'Customer:Process:Status',
      Value: 'Registered',
    },
    {
      Name: 'Customer:Settings:Application:Language',
      Value: 'en-US',
    },
    {
      Name: 'Customer:Settings:Notification:Email',
      Value: '1',
    },
    {
      Name: 'Customer:Settings:Tutorial:Agent',
      Value: 'BRUCE',
    },
    {
      Name: 'Customer:Network:Open',
      Value: '0',
    },
    {
      Name: 'Customer:Terms:Accept',
      Value: todayISOString,
    },
  ]

  const response = await fetch(`${API_BASE_URL}/Register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  })

  return await response.json()
}

export function setAuthToken(token: string): void {
  localStorage.setItem('auth_token', token)
}

export function getAuthToken(): string | null {
  return localStorage.getItem('auth_token')
}

export function removeAuthToken(): void {
  localStorage.removeItem('auth_token')
}

export function isAuthenticated(): boolean {
  return !!getAuthToken()
}
