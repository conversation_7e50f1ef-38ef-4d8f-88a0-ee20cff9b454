import dayjs from 'dayjs'
import { jwtDecode } from 'jwt-decode'
import {
  getLocalStorageUserData,
  // removeUser,
  updateUserToken,
} from '../helpers/storage'
import { CollectionsType } from '../types/app'
import { useTokenRefresh } from '@/hooks/useTokenRefresh'

let retryRequest = false

export const getAuthHeaders = (
  token: string | null,
): { [key: string]: string } => ({
  Authorization: `Bearer ${token}`,
})

export const request = async <T = Response>(
  url: string,
  ...args: [RequestInit | undefined]
): Promise<T> => {
  const user = getLocalStorageUserData()
  const userToken = user ? user['Custom:User:Token'] : ''

  const originalRequest = async (newToken: string): Promise<Response> => {
    const headers = args?.length
      ? { ...args[0]?.headers, Authorization: `Bearer ${newToken}` }
      : {}
    const newArgs = args?.length ? [{ ...args[0], headers }] : args
    const response = await fetch(url, ...newArgs)
    return response
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onError = async (error: any): Promise<never> => {
    const {
      exp,
    }: {
      exp: number
    } = jwtDecode(userToken)
    const now = dayjs().unix()
    const refreshToken = user?.['Custom:Refresh:Token']

    if (refreshToken && !retryRequest && now > exp) {
      retryRequest = true
      const renewUrl = 'https://bgj.oc-club.community/Interop/RenewToken'
      const newTokens = await fetch(renewUrl, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'Accept-Charset': 'utf-8',
        },
        method: 'POST',
        body: JSON.stringify([
          {
            Name: 'Custom:User:Token',
            Value: userToken,
          },
          {
            Name: 'Custom:Refresh:Token',
            Value: refreshToken,
          },
          {
            Name: 'Custom:Device:UniqueId',
            Value: localStorage.getItem('Custom:Device:UniqueId'),
          },
        ]),
      })

      const tokens = (await newTokens.json()) as CollectionsType

      if (newTokens?.ok && !tokens?.Error) {
        updateUserToken(tokens.Properties)
        retryRequest = false
        const newToken = tokens?.Properties?.find(
          (prop) => prop.Name === 'Custom:User:Token',
        )?.Value
        if (newToken) originalRequest(newToken)
      } else {
        // Instead of removing user data, mark the session as expired
        if (user) {
          localStorage.setItem('widgetSessionExpired', 'true')
          // Keep the user data but invalidate the token
          localStorage.removeItem('widgetUserToken')
        }
      }
    }

    return Promise.reject(error.response || error.message)
  }

  const response = await fetch(url, ...args)

  if (response.status === 401) {
    const { exp }: { exp: number } = jwtDecode(userToken)
    const now = dayjs().unix()

    if (now > exp) {
      await onError('Authorization error')
    }
  }

  return response as T
}

export const fetcher = async <T>(
  url: string,
  ...args: [RequestInit | undefined]
): Promise<T> => {
  if (!url) throw new Error('No endpoint')

  const user = getLocalStorageUserData()
  const userToken = user ? user['Custom:User:Token'] : null

  const arg = args?.length ? args[0] : {}

  const additional =
    url?.includes('clientreport') || url.includes('https://cdn')
      ? {}
      : getAuthHeaders(userToken)

  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'Accept-Charset': 'utf-8',
    ...additional,
  }

  const newArgs = [
    {
      ...arg,
      headers: {
        ...headers,
        ...arg?.headers,
      },
    },
  ] as [RequestInit | undefined]
  try {
    const response = await request(url, ...newArgs)

    if ([204, 202].includes(response.status)) {
      return {
        status: response.status,
        ok: response.ok,
      } as T
    }

    if (url.endsWith('.vtt')) {
      return (await response.text()) as T
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching data:', error)
    return {} as T
  }
}

export const multipleFetcher = async <T>(
  urls: string[],
  ...args: [RequestInit | undefined]
): Promise<T[]> =>
  await Promise.all<T>(urls.map((url) => fetcher(url, ...args)))

export const fileFetcher = async <T>(
  url: string,
  ...args: [RequestInit | undefined]
): Promise<T> => {
  if (!url) throw new Error('No endpoint')

  const user = getLocalStorageUserData()
  const userToken = user ? user['Custom:User:Token'] : null

  const arg = args?.length ? args[0] : {}

  const headers = {
    Accept: 'application/json',
    'Accept-Charset': 'utf-8',
    ...getAuthHeaders(userToken),
  }

  const newArgs = [
    {
      ...arg,
      headers,
    },
  ] as [RequestInit | undefined]

  const response = await request(url, ...newArgs)

  if ([204, 202].includes(response.status))
    return {
      status: response.status,
      ok: response.ok,
    } as T

  if (response.ok) {
    return await response.json()
  } else {
    throw new Error('Failed to fetch the file')
  }
}

export const apiCallWithTokenCheck = async <T>(
  url: string,
  options: RequestInit,
): Promise<T> => {
  const { ensureValidTokenForApiCall } = useTokenRefresh()

  return ensureValidTokenForApiCall(async () => {
    const response = await fetch(url, options)
    if (!response.ok) {
      throw new Error(`API call failed with status: ${response.status}`)
    }
    return response.json()
  })
}
