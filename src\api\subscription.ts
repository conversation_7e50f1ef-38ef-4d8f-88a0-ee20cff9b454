import { PaymentIntent, PaymentMethod } from '@stripe/stripe-js'
import { CustomerAccount, Invoice, InvoiceItem } from '../types/subscription'
import { getLocalStorageUserData } from '../helpers/storage'
import { fetcher } from '.'
import { CollectionsType } from '@/types/app'
const secretKey =
  'sk_test_51RecxOIhAqXGxPPc847bIowjlZ24HHUaiHuquVytu3PkFl0bpN4D6y8f3NMjW0wTsSSVt1rD5tgZHJRnF0o2W0xI00jmvDtFaB'

export const createPaymentIntent = async (
  amount = 1000,
  currency = 'usd',
): Promise<PaymentIntent> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('amount', amount.toString())
  urlencoded.append('currency', currency)
  urlencoded.append('payment_method_types[]', 'card')

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch(
    'https://api.stripe.com/v1/payment_intents',
    requestOptions,
  ).then((response) => response.json())
}

export const getUserStripeAccountById = async (
  userId: string,
): Promise<CustomerAccount> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const requestOptions = {
    method: 'GET',
    headers,
  }

  return fetch(
    `https://api.stripe.com/v1/customers/${userId}`,
    requestOptions,
  ).then((response) => response.json())
}

export const createStripeCustomer = async (
  paymentMethodId: string,
  name?: string,
): Promise<CustomerAccount | undefined> => {
  const user = getLocalStorageUserData()
  if (!user) return

  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('name', name || user['Custom:User:Name'])
  urlencoded.append('email', user['Custom:User:Email'])
  urlencoded.append('metadata[customerId]', user['Custom:User:Id'])
  urlencoded.append('payment_method', paymentMethodId)
  urlencoded.append('invoice_settings[default_payment_method]', paymentMethodId)

  const requestOptions = {
    method: 'POST',
    headers,
    body: JSON.stringify(urlencoded),
  }

  return fetch('https://api.stripe.com/v1/customers', requestOptions)
    .then((response) => response.json())
    .catch((e) => console.log('createStripeCustomer error', e))
}

export const detachPaymentMethod = async (
  paymentMethodId: string,
): Promise<PaymentMethod> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const requestOptions = {
    method: 'POST',
    headers,
  }

  return fetch(
    `https://api.stripe.com/v1/payment_methods/${paymentMethodId}/detach`,
    requestOptions,
  ).then((response) => response.json())
}

export const attachPaymentMethodToCustomer = async (
  paymentMethodId: string,
  customerId: string,
): Promise<PaymentMethod> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('customer', customerId)

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch(
    `https://api.stripe.com/v1/payment_methods/${paymentMethodId}/attach`,
    requestOptions,
  )
    .then((response) => response.json())
    .catch((e) => console.log('attachPaymentMethodToCustomer', e))
}

export const updateStripeCustomer = async (
  customerId: string,
  paymentMethodId: string,
): Promise<CustomerAccount> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('invoice_settings[default_payment_method]', paymentMethodId)

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch(
    `https://api.stripe.com/v1/customers/${customerId}`,
    requestOptions,
  )
    .then((response) => response.json())
    .catch((e) => console.log('updateStripeCustomer', e))
}

export const createInvoiceItem = async (
  priceId: string,
  customerId: string,
): Promise<InvoiceItem> => {
  const user = getLocalStorageUserData()
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('customer', customerId)
  urlencoded.append('price', priceId)
  urlencoded.append('metadata[customerId]', user ? user['Custom:User:Id'] : '')

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch('https://api.stripe.com/v1/invoiceitems', requestOptions)
    .then((response) => response.json())
    .catch((e) => console.log('createInvoiceItem', e))
}

export const createInvoice = async (
  customerId: string,
  paymentMethodId: string,
): Promise<Invoice> => {
  const user = getLocalStorageUserData()
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()
  urlencoded.append('customer', customerId)
  urlencoded.append('default_payment_method', paymentMethodId)
  urlencoded.append('pending_invoice_items_behavior', 'include')
  urlencoded.append('auto_advance', 'true')
  urlencoded.append('metadata[customerId]', user ? user['Custom:User:Id'] : '')

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch('https://api.stripe.com/v1/invoices', requestOptions)
    .then((response) => response.json())
    .catch((e) => console.log('createInvoice', e))
}

export const finalizeInvoice = async (invoiceId: string): Promise<Invoice> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch(
    `https://api.stripe.com/v1/invoices/${invoiceId}/finalize`,
    requestOptions,
  )
    .then((response) => response.json())
    .catch((e) => console.log('finalizeInvoice', e))
}

export type PayInvoiceErrorResponce = {
  message: string
  request_log_url: string
  type: string
}

export const payInvoice = async (
  invoiceId: string,
): Promise<Invoice | PayInvoiceErrorResponce> => {
  const headers = new Headers()
  headers.append('Authorization', 'Bearer ' + secretKey)
  headers.append('Content-Type', 'application/x-www-form-urlencoded')

  const urlencoded = new URLSearchParams()

  const requestOptions = {
    method: 'POST',
    headers,
    body: urlencoded,
  }

  return fetch(
    `https://api.stripe.com/v1/invoices/${invoiceId}/pay`,
    requestOptions,
  )
    .then(async (response) => {
      const result = await response.json()
      if (!response.ok) {
        return {
          message: result.error.message || 'An error occurred',
          request_log_url: result.request_log_url || '',
          type: result.type || 'unknown_error',
        }
      }
      return result
    })
    .catch((e) => {
      console.log('payInvoice error:', e)
      throw new Error('payInvoice error: ' + e)
    })
}

export const getExternalIds = (
  url: string,
): CollectionsType | Promise<CollectionsType> => fetcher(url, {})

export const updateExternalIds = (
  url: string,
  stripeCustomerId: string,
): CollectionsType | Promise<CollectionsType> => {
  const body = JSON.stringify([
    {
      Name: 'Custom:ExternalIds:Stripe',
      Value: stripeCustomerId,
    },
  ])

  return fetcher(url, { method: 'post', body: body })
}
