import { actionsParser, transformResponse } from '@/helpers'
import { CollectionsType } from '@/types/app'
import { UserActions, UserData } from '@/types/user'
import { fetcher } from '.'

export async function getCustomerById(
  id: string | null,
): Promise<UserData | null> {
  const url = id
    ? 'https://cdn.oc-club.community/api/v6/customer/${contentId}/entity.json'.replace(
        '${contentId}',
        id,
      )
    : null

  if (!url) {
    return null
  }

  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(
        `Failed to fetch user: ${response.status} ${response.statusText}`,
      )
    }

    const data = await response.json()
    const actions = actionsParser(data.Actions)
    return {
      ...(transformResponse(data.Properties) as unknown as UserData),
      Actions: actions as unknown as UserActions,
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  }
}

export const updateUser = async (
  customerId: string,
  data: Partial<UserData>,
): Promise<CollectionsType> => {
  const url =
    'https://bgj.oc-club.community/Interop/customer/{customerId}/partial'.replace(
      '{customerId}',
      customerId,
    )

  const token = localStorage.getItem('widgetUserToken')

  const body = JSON.stringify([
    ...Object.entries(data).map(([Name, Value]) => ({ Name, Value })),
  ])

  return fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body,
  }).then(async (response) => {
    if (!response.ok) {
      throw new Error(
        `Failed to update user: ${response.status} ${response.statusText}`,
      )
    }
    const text = await response.text()
    if (!text) {
      return {} as CollectionsType
    }

    return JSON.parse(text) as CollectionsType
  })
}

export const removeCustomerSoft = (customerId: string): Promise<void> => {
  const url = `https://bgj.oc-club.community/api/v2.1/Customer/${customerId}/1`

  return fetcher(url, { method: 'delete' })
}
