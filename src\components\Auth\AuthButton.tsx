import { useAppContext } from '@/context/AppContext'

type AuthButtonProps = {
  provider: {
    id: 'apple' | 'google' | 'facebook'
    icon: React.ReactNode
    name: string
  }
  onClick: (id: 'apple' | 'google' | 'facebook') => void
}

const AuthButton = ({ provider, onClick }: AuthButtonProps) => {
  const { colorsInfo } = useAppContext()

  const backgroundColor = colorsInfo?.secondaryBackgroundColor
  const fontColor = colorsInfo?.fontColor

  return (
    <button
      onClick={() => onClick(provider.id)}
      style={{
        backgroundColor: backgroundColor || undefined,
        color: fontColor || undefined,
        borderColor: backgroundColor || undefined,
      }}
      className="w-full flex items-center justify-center gap-3 px-4 py-2 bg-white hover:bg-ring
                 text-gray-900 font-medium rounded-md border border-ring shadow-sm 
                 transition-colors duration-200 ease-in-out"
    >
      {provider.icon}
      <span>Continue with {provider.name}</span>
    </button>
  )
}

export default AuthButton
