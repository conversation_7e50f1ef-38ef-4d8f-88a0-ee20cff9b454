import { Button } from '@/components/ui/button'
import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

interface CommunityBannerProps {
  readonly title?: string
  readonly communityName?: string
  readonly ownerName?: string
  readonly description?: string
  readonly perks?: readonly string[]
  readonly onJoin?: () => void
  readonly titleKey?: string
  readonly bodyKey?: string
  readonly buttonKey?: string
}

export default function CommunityBanner({
  onJoin,
  titleKey = 'APP_LOYALTY_SIGNEDOUT_JOINNOW_TITLE',
  bodyKey = 'APP_LOYALTY_SIGNEDOUT_JOINNOW_BODY',
  buttonKey = 'APP_LOYALTY_SIGNEDOUT_BUTTON_TEXT',
}: CommunityBannerProps) {
  const { colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const textColor = colorsInfo?.secondaryText
  const fontColor = colorsInfo?.fontColor

  return (
    <div className="w-full text-foreground rounded-md border-2 border-muted-foreground p-2">
      <h3
        style={{ color: titleColor || undefined }}
        className="text-accent-3 font-semibold mb-4"
      >
        <div dangerouslySetInnerHTML={{ __html: t(titleKey) }} />
      </h3>

      <p style={{ color: textColor || undefined }} className="text-base mb-2">
        <div dangerouslySetInnerHTML={{ __html: t(bodyKey) }} />
      </p>

      {onJoin && (
        <Button
          style={{
            backgroundColor: titleColor || undefined,
            color: fontColor || undefined,
          }}
          className="w-full mb-4 py-4 text-sm font-bold bg-accent-3 hover:bg-accent-3 text-foreground-inverse rounded-md"
          onClick={onJoin}
        >
          <div dangerouslySetInnerHTML={{ __html: t(buttonKey) }} />
        </Button>
      )}
    </div>
  )
}
