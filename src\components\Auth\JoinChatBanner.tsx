import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

import { Chat, useSetUserData } from 'side-chat'
import { useEffect, useMemo, useState } from 'react'
import { useMessageModeration } from '@/hooks/useMessageModeration'
import { useUser } from '@/context/UserContext'
import {
  useIncentiveTracker,
  IncentiveEventType,
} from '@/context/IncentiveTrackerContext'
import { useAppContext } from '@/context/AppContext'

interface JoinChatBannerProps {
  readonly onJoinChat?: () => void
}

export default function JoinChatBanner({ onJoinChat }: JoinChatBannerProps) {
  const { artistId, colorsInfo, config } = useAppContext()
  const { userData } = useUser()
  const { error, checkMessage, setError } = useMessageModeration()
  const { trackEvent } = useIncentiveTracker()

  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768)

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Generate user data only once
  const chatUserData = useMemo(() => {
    return {
      userName:
        userData?.['Custom:User:Nickname'] ||
        userData?.['Custom:User:Name'] ||
        'Anonymous',
      _id: userData?.['Content:Id'] || 'unknown-id',
      avatar: userData?.['Custom:User:Avatar'] || '',
    }
  }, [userData])

  useSetUserData(chatUserData)

  // Combine message moderation with incentive tracking
  const handleBeforeMessageSent = async (message: string) => {
    // First check if message passes moderation
    const moderationResult = await checkMessage(message)

    // If message passes moderation, track the event
    if (moderationResult) {
      // Track the chat message event (will award incentive if not already awarded)
      trackEvent(IncentiveEventType.CHAT_MESSAGE_SENT).catch((err) => {
        console.error('Failed to track chat message event:', err)
      })
    }

    return moderationResult
  }

  const secondaryBackgroundColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor
  const secondaryText = colorsInfo?.secondaryText
  const fontColor = colorsInfo?.fontColor

  return (
    <Card
      style={{ backgroundColor: secondaryBackgroundColor || undefined }}
      className="w-full bg-background text-foreground border-0 rounded-xl overflow-hidden"
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-semibold mb-2">
          {config?.name === 'Walk off the Earth'
            ? `Live Chat`
            : 'Community Chat'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="h-full pt-2 box-border">
          <Chat
            beforeSentMessage={handleBeforeMessageSent}
            onInputChange={async () => {
              if (error) {
                setError(false)
              }
              return Promise.resolve(true)
            }}
            roomId={`comunity-chat-${artistId}`}
            styles={{
              mainBlock: {
                backgroundColor: 'transparent',
                height: '100%',
                padding: '0',
              },
              inputBlock_input: {
                backgroundColor: error ? 'red' : 'white',
                color: error ? 'red' : 'initial',
              },
              scrollBlock: {
                WebkitOverflowScrolling: 'touch',
                minHeight: isMobile ? 'auto' : 'calc(100% - 50px)',
                overflowY: 'auto',
                gap: '1.25rem',
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              },
              scrollBlock_messageBlock_messageContainer: {
                backgroundColor: 'unset',
                padding: '0',
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              },
              scrollBlock_messageBlock_timestamp: {
                textAlign: 'right',
                color: fontColor || '#A1F425',
                width: '15%',
                position: 'relative',
                bottom: '28px',
                fontWeight: 'medium',
                fontSize: '12px',
              },
              scrollBlock_messageBlock: {
                maxWidth: '100%',
                width: '100%',
                minWidth: '100%',
              },
              scrollBlock_messageBlock_content: {
                minWidth: '90%',
              },
              scrollBlock_messageBlock_message: {
                backgroundColor: 'unset',
                padding: '0',
                color: backgroundColor || '#FFFFFF',
                position: 'relative',
                top: '16px',
                right: '50px',
                marginTop: '16px',
                marginBottom: '20px',
              },
              scrollBlock_messageBlock_title: {
                marginBottom: '0',
                color: fontColor || '#A1F425',
                position: 'relative',
                top: '10px',
                fontSize: '12px',
              },
              inputBlock_submitIcon: {
                size: 0,
              },
              inputBlock: {
                display: 'none',
                height: 0,
              },
            }}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={onJoinChat}
          className="w-full py-6 text-sm font-medium bg-secondary hover:bg-zinc-700 text-white rounded-md"
          style={{
            backgroundColor: secondaryText || undefined,
            opacity: secondaryText ? 0.5 : 1,
            color: fontColor || '#FFFFFF',
          }}
        >
          Join the Chat
        </Button>
      </CardFooter>
    </Card>
  )
}
