import { JSX, useCallback, useEffect, useRef, useState } from 'react'
import { jwtDecode } from 'jwt-decode'
import RegistrationForm from './RegistrationForm'
import CommunityBanner from './CommunityBanner'
import AuthButton from './AuthButton'
import TermsForm from './TermsForm'
import { useAuth } from '@/context/AuthContext'
import { AuthSocialData } from '@/types/auth'
import { UserData } from '@/types/user'
import { AppleLogo, FacebookLogo, GoogleLogo } from '../Icons'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'
import { Alert, AlertDescription } from '../ui/alert'
import WOTE_logo from '../../assets/WOTE_logo.svg'

const authProviders: {
  id: 'google' | 'apple' | 'facebook'
  icon: JSX.Element
  name: string
}[] = [
  {
    icon: <GoogleLogo />,
    name: 'Google',
    id: 'google',
  },
  {
    icon: <AppleLogo />,
    name: '<PERSON>',
    id: 'apple',
  },
  {
    icon: <FacebookLogo />,
    name: 'Facebook',
    id: 'facebook',
  },
]

const Login = () => {
  const { login, registerUser, sessionExpired } = useAuth()
  const {
    setIsMenuOpen,
    sessionExpiredMessage,
    setSessionExpiredMessage,
    colorsInfo,
    config,
  } = useAppContext()

  const [prefilData, setPrefilData] = useState<Partial<UserData> | undefined>(
    undefined,
  )
  const [registrationFormVisible, setShowRegistrationForm] = useState(false)
  const [authButtonVisible, setAuthButtonVisible] = useState(true)
  const [showTermsForm, setShowTermsForm] = useState(false)
  const tokenCredentialsData = useRef<AuthSocialData>({ credential: '' })
  const [popup, setPopup] = useState<Window | null>()

  useEffect(() => {
    return () => {
      setSessionExpiredMessage(null)
    }
  }, [setSessionExpiredMessage])

  const handleLogin = (type: 'google' | 'apple' | 'facebook'): void => {
    console.log('🚀 Login button clicked:', type)
    localStorage.setItem('provider', type)

    const links = {
      facebook: 'https://bgj.oc-club.community/Facebook/signin',
      google: 'https://bgj.oc-club.community/Google/signin',
      apple: 'https://bgj.oc-club.community/Apple/signin',
    }

    const link = links[type] + `?state=webmodal%23%23`

    setPopup(window.open(link, 'popup=true,width=500,height=500,left=500'))
  }

  const handleAuthenticateUser = useCallback(
    async (userData: AuthSocialData): Promise<void> => {
      console.log(
        '👤 Authenticating user with credential length:',
        userData.credential?.length,
      )
      try {
        const authResponse = await login(userData)

        if (
          authResponse.errorCode &&
          ['NOT_REGISTERED', 'ERROR_UNAUTHORIZED'].includes(
            authResponse.errorCode as string,
          )
        ) {
          setShowTermsForm(true)

          if (authResponse.properties) {
            const propertiesObj = authResponse.properties.reduce(
              (acc, item) => {
                acc[item.Name] = item.Value
                return acc
              },
              {} as Record<string, string>,
            )

            setPrefilData((prevData) => ({
              ...prevData,
              ...propertiesObj,
            }))
          }
        } else {
          if (authResponse.success) {
            console.log('authResponse:', authResponse)
            setSessionExpiredMessage(null)
          }
        }
      } catch (error) {
        console.log('error:', error)
      }
    },
    [login, setSessionExpiredMessage],
  )

  const handleRegisterUser = useCallback(async (userData: UserData) => {
    try {
      if (
        !tokenCredentialsData.current ||
        tokenCredentialsData.current.credential === null
      ) {
        console.log('No token found')
        return
      }
      const response = await registerUser({
        ...userData,
        credential: tokenCredentialsData.current.credential,
      })
      if (response.success) {
        console.log('User registered successfully')
        handleAuthenticateUser(tokenCredentialsData.current)
      }
    } catch (error) {
      console.log('error:', error)
    }
  }, [])

  const getFacebookData = useCallback(
    async (token: string): Promise<void> => {
      try {
        const response = await fetch(
          'https://graph.facebook.com/v18.0/me?fields=id,name,email,picture&access_token=' +
            token,
        )
        const data = await response?.json()
        if (data.error) {
          console.log('data.error:', data.error)
        } else {
          const parsed = JSON.stringify({
            picture: data?.picture?.data?.url,
            accessToken: token,
            email: data?.email,
            name: data?.name,
            sub: data?.id,
          })

          setPrefilData({
            'Custom:User:Avatar': data?.picture?.data?.url,
            'Custom:User:Email': data?.email,
            'Custom:User:Name': data?.name,
          })

          tokenCredentialsData.current = {
            credential: parsed,
          }
          handleAuthenticateUser({
            credential: parsed,
          })
        }
      } catch (error) {
        console.log('error:', error)
      }
    },
    [handleAuthenticateUser],
  )

  const handleToken = useCallback(
    (token: string) => {
      console.log(
        '🔑 Token received, provider:',
        localStorage.getItem('provider'),
      )
      const provider = localStorage.getItem('provider')

      if (provider === 'google' || provider === 'apple') {
        try {
          const tokenParsedData = JSON.parse(window.atob(token)) as {
            id_token: string
          }

          handleAuthenticateUser({
            credential: tokenParsedData.id_token,
          })

          tokenCredentialsData.current = {
            credential: tokenParsedData.id_token,
          }

          const tokenDecodetData = jwtDecode<{
            name: string
            email: string
            picture: string
          }>(tokenParsedData.id_token)

          setPrefilData({
            'Custom:User:Avatar': tokenDecodetData.picture,
            'Custom:User:Email': tokenDecodetData.email,
            'Custom:User:Name': tokenDecodetData.name,
          })
        } catch (error) {
          console.log('parsed token error:', error)
        }
      } else if (provider === 'facebook') {
        const tokenParsedData = JSON.parse(window.atob(token)) as {
          access_token: string
        }

        getFacebookData(tokenParsedData.access_token)
      } else {
        console.log('unknown provider')
      }
    },
    [handleAuthenticateUser, getFacebookData],
  )

  useEffect(() => {
    async function loginListener(event: MessageEvent): Promise<void> {
      console.log(
        '📨 Message received from popup:',
        event.data?.substring?.(0, 30),
      )
      if (typeof event.data === 'string') {
        handleToken(event.data)
        popup?.close()
      }
    }
    window.addEventListener('message', loginListener)
  }, [handleToken, popup])

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor

  return (
    <div className="flex min-h-[100dvh] flex-col px-6 py-5 w-full">
      <div
        style={{ backgroundColor: backgroundColor || undefined }}
        className="flex-grow flex flex-col items-center bg-sidebar-hub-background"
      >
        {sessionExpired && sessionExpiredMessage && (
          <Alert className="mb-4 bg-yellow-500/20 border-yellow-500 text-yellow-300">
            <AlertDescription>{sessionExpiredMessage}</AlertDescription>
          </Alert>
        )}

        {showTermsForm && !registrationFormVisible && (
          <TermsForm
            onContinue={() => {
              setShowRegistrationForm(true)
              setAuthButtonVisible(false)
              setShowTermsForm(false)
            }}
            prefilData={prefilData}
          />
        )}
        {registrationFormVisible && (
          <RegistrationForm
            onRegister={handleRegisterUser}
            prefilData={prefilData}
          />
        )}

        {authButtonVisible && !showTermsForm && (
          <div>
            <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
              <button
                style={{ color: titleColor || '#22D3EE' }}
                className="flex flex-col justify-center md:hidden"
                onClick={() => setIsMenuOpen(true)}
              >
                <MobileMenuIcon />
              </button>
              {!colorsInfo?.logo ? (
                <h1
                  style={{ color: titleColor || undefined }}
                  className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
                >
                  Official Community
                </h1>
              ) : (
                <img src={WOTE_logo} alt="WOTE Logo" className="h-10 w-auto" />
              )}
            </div>
            <div className="flex flex-col items-center space-y-4 w-full">
              <CommunityBanner
                titleKey={
                  config?.name === 'Walk off the Earth'
                    ? 'APP_HOME_SIGNEDOUT_WOTE_JOIN_TITLE'
                    : 'APP_LOYALTY_SIGNEDOUT_SSO_TITLE'
                }
                bodyKey={
                  config?.name === 'Walk off the Earth'
                    ? 'APP_HOME_SIGNEDOUT_WOTE_JOIN_BODY'
                    : 'APP_LOYALTY_SIGNEDOUT_SSO_BODY'
                }
                buttonKey="APP_LOYALTY_SIGNEDOUT_BUTTON_TEXT"
              />
              <div className="space-y-4 w-4/5">
                {authProviders.map((provider) => (
                  <AuthButton
                    onClick={() => handleLogin(provider.id)}
                    key={provider.name}
                    provider={provider}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Login
