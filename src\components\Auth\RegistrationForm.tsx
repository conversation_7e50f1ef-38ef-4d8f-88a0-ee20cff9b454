import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { UserData } from '@/types/user'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'
import { DatePicker } from '../ui/datePicker'

type RegistrationFormProps = {
  onRegister: (userData: UserData) => void
  prefilData?: Partial<UserData>
}
const RegistrationForm = ({
  onRegister,
  prefilData,
}: RegistrationFormProps) => {
  const { setIsMenuOpen, colorsInfo } = useAppContext()

  const [date, setDate] = useState<Date | undefined>(undefined)

  const {
    formState: { errors },
    handleSubmit,
    register,
    setValue,
  } = useForm<UserData>()

  // Register the birthday field and handle prefilled data
  useEffect(() => {
    register('Custom:User:Birthday')

    // Handle prefilled birthday data
    if (prefilData?.['Custom:User:Birthday']) {
      try {
        // Parse date in format DD/MM/YYYY
        const [day, month, year] = prefilData['Custom:User:Birthday'].split('/')
        // Note: month is 0-indexed in JavaScript Date
        const birthDate = new Date(Number(year), Number(month) - 1, Number(day))

        // Check if date is valid
        if (!isNaN(birthDate.getTime())) {
          setDate(birthDate)
          setValue('Custom:User:Birthday', prefilData['Custom:User:Birthday'])
        }
      } catch (error) {
        console.error('Invalid date format:', error)
      }
    }
  }, [register, prefilData, setValue])

  useEffect(() => {
    if (prefilData?.['Custom:User:Avatar']) {
      register('Custom:User:Avatar', {
        value: prefilData['Custom:User:Avatar'],
      })
    }
  }, [prefilData, register])

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <form
      className="w-full text-white space-y-6"
      onSubmit={(e) => {
        // Always prevent default form submission
        e.preventDefault()

        // Get the active element when form is submitted
        const activeElement = document.activeElement

        // Only process form if the active element is a button
        if (
          activeElement instanceof HTMLButtonElement &&
          activeElement.type === 'submit'
        ) {
          handleSubmit(onRegister)(e)
        }
      }}
    >
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <h1
          style={{ color: titleColor || undefined }}
          className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
        >
          Official Community
        </h1>
      </div>

      <h1 className="text-xl font-semibold text-accent text-left">
        {prefilData?.['Custom:User:Name']
          ? `Welcome, ${prefilData?.['Custom:User:Name']}!`
          : 'Welcome'}
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
        <div className="space-y-2">
          <div className="space-y-0">
            <Label htmlFor="Custom:User:Name">Name</Label>
            <Input
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-foreground-inverse"
              defaultValue={prefilData?.['Custom:User:Name'] || ''}
              {...register('Custom:User:Name', {
                required: 'Name is required',
              })}
              id="Custom:User:Name"
            />
            {errors['Custom:User:Name'] && (
              <p className="text-red-500 text-sm">
                {errors['Custom:User:Name'].message}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="Custom:User:Nickname">Nickname (optional)</Label>
            <Input
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-foreground-inverse"
              {...register('Custom:User:Nickname')}
              id="Custom:User:Nickname"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="Custom:User:Email">Email</Label>
            <Input
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-foreground-inverse"
              defaultValue={prefilData?.['Custom:User:Email'] || ''}
              id="Custom:User:Email"
              {...register('Custom:User:Email', {
                required: 'Email is required',
              })}
              type="email"
            />
            {errors['Custom:User:Email'] && (
              <p className="text-red-500 text-sm">
                {errors['Custom:User:Email']?.message}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="Custom:User:Phone">Phone Number (optional)</Label>
            <Input
              id="Custom:User:Phone"
              {...register('Custom:User:Phone')}
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-muted-foreground"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="Custom:User:Birthday">Birthday (optional)</Label>
            <div className="relative">
              <div
                onClick={(e) => {
                  // Prevent form submission
                  if (e.target === e.currentTarget) {
                    e.preventDefault()
                  }
                }}
                onMouseDown={(e) => {
                  // Prevent blur events on other form fields
                  e.preventDefault()
                }}
              >
                <DatePicker
                  date={date}
                  setDate={(newDate) => {
                    setDate(newDate)

                    if (newDate && !isNaN(newDate.getTime())) {
                      const day = String(newDate.getDate()).padStart(2, '0')
                      const month = String(newDate.getMonth() + 1).padStart(
                        2,
                        '0',
                      )
                      const year = newDate.getFullYear()
                      const formattedDate = `${day}/${month}/${year}`
                      setValue('Custom:User:Birthday', formattedDate)
                    } else {
                      setValue('Custom:User:Birthday', '')
                    }
                  }}
                />
              </div>
              {errors['Custom:User:Birthday'] && (
                <p className="text-red-500 text-sm">
                  {errors['Custom:User:Birthday'].message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="Customer:Location:City">City</Label>
            <Input
              id="Customer:Location:City"
              {...register('Customer:Location:City', {
                required: 'City is required',
              })}
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-foreground-inverse"
            />
            {errors['Customer:Location:City'] && (
              <p className="text-red-500 text-sm">
                {errors['Customer:Location:City'].message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="Customer:Location:Country">Country</Label>
            <Input
              id="Customer:Location:Country"
              {...register('Customer:Location:Country', {
                required: 'Country is required',
              })}
              className="w-full focus-visible:ring-primary focus-visible:border-0 bg-foreground text-foreground-inverse"
            />
            {errors['Customer:Location:Country'] && (
              <p className="text-red-500 text-sm">
                {errors['Customer:Location:Country'].message}
              </p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button className="w-80 hover:bg-primary text-black">Continue</Button>
      </div>
    </form>
  )
}

export default RegistrationForm
