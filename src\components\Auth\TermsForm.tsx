import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { UserData } from '@/types/user'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'

type TermsFormProps = {
  onContinue: () => void
  prefilData?: Partial<UserData>
}

const TermsForm = ({ onContinue, prefilData }: TermsFormProps) => {
  const { setIsMenuOpen, colorsInfo } = useAppContext()

  const [checkboxes, setCheckboxes] = useState({
    terms: false,
    privacy: false,
    newsletter: false,
  })

  const handleCheckboxChange = (name: keyof typeof checkboxes) => {
    setCheckboxes((prev) => ({ ...prev, [name]: !prev[name] }))
  }

  const isAllChecked = checkboxes.terms && checkboxes.privacy
  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="w-full space-y-6 text-foreground">
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <h1
          style={{ color: titleColor || undefined }}
          className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
        >
          Official Community
        </h1>
      </div>

      <h1 className="text-xl font-semibold text-accent text-left">
        {prefilData?.['Custom:User:Name']
          ? `Welcome, ${prefilData?.['Custom:User:Name']}!`
          : 'Welcome'}
      </h1>

      <div className="flex items-center space-x-2 space-y-2">
        <Checkbox
          onCheckedChange={() => handleCheckboxChange('newsletter')}
          checked={checkboxes.newsletter}
          id="newsletter"
        />
        <label
          htmlFor="newsletter"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I’d like to receive special offers and news on my email.
        </label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox
          id="terms"
          checked={checkboxes.terms}
          onCheckedChange={() => handleCheckboxChange('terms')}
        />
        <label
          htmlFor="terms"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I agree to the{' '}
          <a
            href="https://hub.oc-club.community/legal/terms"
            className="text-primary underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            Terms and Conditions
          </a>
          {'.'}
        </label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox
          id="privacy"
          checked={checkboxes.privacy}
          onCheckedChange={() => handleCheckboxChange('privacy')}
        />
        <label
          htmlFor="privacy"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I agree to the{' '}
          <a
            href="https://hub.oc-club.community/legal/privacy"
            className="text-primary underline"
          >
            Data Privacy Notice
          </a>
          {'.'}
        </label>
      </div>
      <div className="flex justify-center">
        <Button
          className="w-1/2 hover:bg-primary text-black mt-6"
          disabled={!isAllChecked}
          onClick={onContinue}
        >
          Continue
        </Button>
      </div>
    </div>
  )
}

export default TermsForm
