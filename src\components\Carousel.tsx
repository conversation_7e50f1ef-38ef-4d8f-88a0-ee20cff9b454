import { useCallback, useEffect, useState } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import {
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  Carousel,
} from '@/components/ui/carousel'
import { CarouselSlide } from '@/types/carousel'

interface CarouselSliderProps {
  readonly slides: CarouselSlide[]
  readonly title?: string
}

export default function CarouselSlider({
  title,
  slides = [],
}: CarouselSliderProps) {
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!api) {
      return
    }

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap())

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap())
    })
  }, [api])

  const handlePrevious = useCallback(() => {
    api?.scrollPrev()
  }, [api])

  const handleNext = useCallback(() => {
    api?.scrollNext()
  }, [api])

  const handleSelect = useCallback(
    (index: number) => {
      api?.scrollTo(index)
    },
    [api],
  )

  const handleNavigation = (slideId: string) => {
    console.log('slideId:', slideId)
  }

  return (
    <div className="w-full relative space-y-4">
      {title && <h4 className="mb-3">{title}</h4>}

      <div className="relative">
        <Carousel
          setApi={setApi}
          className="w-full flex flex-col gap-4"
          opts={{
            align: 'start',
            loop: true,
            skipSnaps: false,
            slidesToScroll: 1,
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {slides.map((slide) => (
              <CarouselItem
                className="pl-2 md:pl-4 basis-[calc(28.5%-0.5rem)] md:basis-[calc(28.5%-1rem)] cursor-pointer"
                onClick={() => handleNavigation(slide.id)}
                key={slide.id}
              >
                <div className="relative aspect-[4/3]">
                  <img
                    className="w-full h-full object-cover rounded-lg"
                    src={slide.image || '/placeholder.svg'}
                    alt={slide.title}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          <div className="flex items-center justify-center gap-4 md:gap-8">
            <button onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4 md:h-6 md:w-6 text-white" />
            </button>

            <div className="flex gap-1 md:gap-2">
              {Array.from({ length: count }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleSelect(index)}
                  className={`h-1 md:h-2 w-1 md:w-2 rounded-full transition-colors ${
                    index === current ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>

            <button onClick={handleNext}>
              <ChevronRight className="h-4 w-4 md:h-6 md:w-6 text-white" />
            </button>
          </div>
        </Carousel>
      </div>
    </div>
  )
}
