import { useState, useEffect, useMemo, useRef } from 'react'
import { Poll, useSetUserData } from 'side-chat'
import { useUser } from '@/context/UserContext'
import { useAppContext } from '@/context/AppContext'
import { getActivePollByArtist } from '@/lib/polls'
import { Skeleton } from '@/components/ui/skeleton'

interface ChatPollProps {
  roomId: string
  className?: string
}

const ChatPoll = ({ roomId, className = '' }: ChatPollProps) => {
  const { userData } = useUser()
  const { config } = useAppContext() // We'll get artist info from config
  const [activePollData, setActivePollData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasInitialLoad, setHasInitialLoad] = useState(false)
  const fetchingRef = useRef(false)

  // Set up user data for the poll system
  const chatUserData = useMemo(() => {
    return {
      userName:
        userData?.['Custom:User:Nickname'] ||
        userData?.['Custom:User:Name'] ||
        'Anonymous',
      _id: userData?.['Content:Id'] || 'unknown-id',
      avatar: userData?.['Custom:User:Avatar'] || '',
    }
  }, [userData])

  useSetUserData(chatUserData)

  // For Widget, we'll use the artistId from roomId (similar to Hub's logic)
  const currentArtist = useMemo(() => {
    // Extract artistId from roomId (remove "comunity-chat-" prefix if present)
    const artistId = roomId.replace('comunity-chat-', '')
    return {
      consoleArtistId: artistId,
      title: config?.name || 'Artist',
    }
  }, [roomId, config])

  // Fetch active poll data (same pattern as Hub)
  useEffect(() => {
    const fetchActivePoll = async () => {
      if (fetchingRef.current) return

      if (!currentArtist?.consoleArtistId) {
        setActivePollData(null)
        if (!hasInitialLoad) {
          setIsLoading(false)
          setHasInitialLoad(true)
        }
        return
      }

      if (!hasInitialLoad) {
        setIsLoading(true)
      }

      fetchingRef.current = true

      try {
        console.log(
          '🔍 ChatPoll - Fetching poll for artistId:',
          currentArtist.consoleArtistId,
        )

        const pollData = await getActivePollByArtist(
          currentArtist.consoleArtistId,
        )

        // Handle array response from API (same as Hub)
        let pollObject = null
        if (Array.isArray(pollData) && pollData.length > 0) {
          pollObject = pollData[0]
        } else if (pollData && !Array.isArray(pollData)) {
          pollObject = pollData
        }

        if (pollObject && pollObject.polls && pollObject.polls.length > 0) {
          const transformedData = {
            streamStartsAt: new Date().toISOString(),
            polls: pollObject.polls.map((poll: any) => ({
              question: poll.question,
              showIn: poll.showIn || 5,
              answerTime: poll.answerTime || 30,
              showResultTime: poll.showResultTime || 10,
              choices: poll.choices.map((choice: any) => ({
                choiceId: choice._id || `choice-${choice.position}`,
                position: choice.position.toString(),
                label: choice.label,
                correct: false,
              })),
            })),
          }
          setActivePollData(transformedData)
        } else {
          setActivePollData(null)
        }
      } catch (error) {
        console.error('💥 ChatPoll - Error fetching active poll:', error)
        setActivePollData(null)
      } finally {
        setIsLoading(false)
        setHasInitialLoad(true)
        fetchingRef.current = false
      }
    }

    if (currentArtist?.consoleArtistId) {
      fetchActivePoll()
      const interval = setInterval(fetchActivePoll, 30000)
      return () => clearInterval(interval)
    } else {
      setActivePollData(null)
      if (!hasInitialLoad) {
        setIsLoading(false)
        setHasInitialLoad(true)
      }
    }
  }, [currentArtist?.consoleArtistId])

  const handleAfterUserAnswer = async (answerData: any) => {
    console.log('User answered poll:', answerData)
    return true
  }

  // Poll Skeleton Component
  const PollSkeleton = () => (
    <div className="space-y-4 p-4">
      <div className="text-center space-y-2">
        <Skeleton className="h-6 w-20 mx-auto" />
        <Skeleton className="h-3 w-32 mx-auto" />
      </div>
      <div className="text-center">
        <Skeleton className="h-5 w-4/5 mx-auto mb-4" />
      </div>
      <div className="space-y-3">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="h-9 px-5 py-1.5 bg-stone-50/5 rounded-lg flex items-center gap-7"
          >
            <Skeleton className="w-3 h-4" />
            <Skeleton className="h-4 flex-1" />
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className={`h-full overflow-y-auto ${className}`}>
      {isLoading ? (
        <PollSkeleton />
      ) : activePollData ? (
        <Poll
          pollsData={activePollData}
          afterUserAnswer={handleAfterUserAnswer}
          artistId={currentArtist?.consoleArtistId || roomId}
          styles={{
            mainBlock: {
              backgroundColor: 'rgba(0, 0, 0, 0.95)',
              borderRadius: '12px',
              color: 'white',
              border: 'none',
            },
            pollBlock_headerTitle: {
              color: '#60a5fa',
              display: 'none',
            },
            pollBlock_headerQuestion: {
              color: 'white',
              fontSize: '18px',
            },
            pollBlock_listItemButton: {
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              border: 'none',
              color: 'white',
            },
          }}
        />
      ) : (
        <div className="text-center text-gray-400 p-8">
          <p>No active polls available</p>
          <p className="text-xs mt-2">Check back later for new polls!</p>
        </div>
      )}
    </div>
  )
}

export default ChatPoll
