import { Chat, useSetUserData } from 'side-chat'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useMessageModeration } from '@/hooks/useMessageModeration'
import { useUser } from '@/context/UserContext'
import {
  useIncentiveTracker,
  IncentiveEventType,
} from '@/context/IncentiveTrackerContext'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { ChatCircle, Question } from '../Icons' // Import your existing icons
import { useAppContext } from '@/context/AppContext'
import ChatPoll from './ChatPoll' // Import our new ChatPoll component

import './index.css'

type TabType = 'chat' | 'poll'

type ChatPanelProps = {
  roomId: string
  title?: string
}

const LiveChat = ({ roomId, title = 'Community Chat' }: ChatPanelProps) => {
  const { userData } = useUser()
  const { error, checkMessage, setError } = useMessageModeration()
  const { trackEvent } = useIncentiveTracker()
  const {
    activeTabDetails,
    setIsMenuOpen,
    colorsInfo,
    loyaltyMatchMerchantId,
  } = useAppContext()

  const scrollBlockRef = useRef<HTMLDivElement | null>(null)
  const [thumbHeight, setThumbHeight] = useState(100)
  const [thumbTop, setThumbTop] = useState(0)
  const [activeTab, setActiveTab] = useState<TabType>('chat') // Add tab state

  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768)

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Generate user data only once
  const chatUserData = useMemo(() => {
    return {
      userName:
        userData?.['Custom:User:Nickname'] ||
        userData?.['Custom:User:Name'] ||
        'Anonymous',
      _id: userData?.['Content:Id'] || 'unknown-id',
      avatar: userData?.['Custom:User:Avatar'] || '',
    }
  }, [userData])

  useSetUserData(chatUserData)

  // Combine message moderation with incentive tracking
  const handleBeforeMessageSent = async (message: string) => {
    // First check if message passes moderation
    const moderationResult = await checkMessage(message)

    // If message passes moderation, track the event
    if (moderationResult) {
      // Track the chat message event (will award incentive if not already awarded)
      trackEvent(IncentiveEventType.CHAT_MESSAGE_SENT, {
        merchantId: loyaltyMatchMerchantId, // Use from context
      }).catch((err) => {
        console.error('Failed to track chat message event:', err)
      })
    }

    return moderationResult
  }

  const handleScroll = () => {
    const container = scrollBlockRef.current
    if (!container) return

    const { scrollTop, scrollHeight, clientHeight } = container

    const heightRatio = clientHeight / scrollHeight
    const newThumbHeight = Math.max(clientHeight * heightRatio, 20)
    const maxThumbTop = clientHeight - newThumbHeight
    const scrollableHeight = scrollHeight - clientHeight
    const newThumbTop = (scrollTop / scrollableHeight) * maxThumbTop

    setThumbHeight(newThumbHeight)
    setThumbTop(newThumbTop)
  }

  useEffect(() => {
    const chatScrollBlock = document.querySelector(
      '._vTrx6SideChat_scrollView_9s3cb_12',
    ) as HTMLDivElement | null

    if (chatScrollBlock) {
      scrollBlockRef.current = chatScrollBlock
      chatScrollBlock.addEventListener('scroll', handleScroll)
      handleScroll()
    }

    return () => {
      if (scrollBlockRef.current) {
        scrollBlockRef.current.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <div className="h-full w-full flex flex-col">
      {title && (
        <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
          <button
            style={{ color: titleColor || '#22D3EE' }}
            className="flex flex-col justify-center md:hidden"
            onClick={() => setIsMenuOpen(true)}
          >
            <MobileMenuIcon />
          </button>
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
          >
            {activeTabDetails ? activeTabDetails.label : title}
          </h1>
        </div>
      )}

      <div className="flex-1 min-h-0 flex flex-col">
        {/* Content Area */}
        <div className="flex-1 min-h-0">
          {activeTab === 'chat' ? (
            // Chat content
            <div className="relative h-full w-full overflow-hidden">
              <Chat
                beforeSentMessage={handleBeforeMessageSent}
                onInputChange={async () => {
                  if (error) setError(false)
                  return Promise.resolve(true)
                }}
                roomId={roomId}
                styles={{
                  mainBlock: {
                    backgroundColor: 'transparent',
                    height: '100%',
                    padding: '0',
                  },
                  inputBlock_input: {
                    backgroundColor: error ? 'red' : 'white',
                    color: error ? 'red' : 'initial',
                    border: secondaryText
                      ? `1px solid ${secondaryText}`
                      : 'none',
                    borderRadius: secondaryText ? '8px' : 0,
                  },
                  scrollBlock: {
                    WebkitOverflowScrolling: 'touch',
                    minHeight: isMobile ? 'auto' : 'calc(100% - 50px)',
                    overflowY: 'auto',
                    gap: '1.25rem',
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  },
                  scrollBlock_messageBlock_messageContainer: {
                    backgroundColor: 'unset',
                    padding: '0',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  },
                  scrollBlock_messageBlock_timestamp: {
                    textAlign: 'right',
                    color: titleColor || '#A1F425',
                    width: '15%',
                    position: 'relative',
                    bottom: '20px',
                    fontWeight: 'medium',
                    fontSize: '12px',
                  },
                  scrollBlock_messageBlock: {
                    maxWidth: '100%',
                    width: '100%',
                    minWidth: '100%',
                  },
                  scrollBlock_messageBlock_content: {
                    minWidth: '88%',
                  },
                  scrollBlock_messageBlock_message: {
                    backgroundColor: 'unset',
                    padding: '0',
                    color: secondaryText || '#FFFFFF',
                    position: 'relative',
                    top: '16px',
                    right: '50px',
                    marginTop: '16px',
                    marginBottom: '20px',
                  },
                  scrollBlock_messageBlock_title: {
                    marginBottom: '0',
                    color: titleColor || '#A1F425',
                    position: 'relative',
                    top: '10px',
                    fontSize: '12px',
                  },
                  inputBlock_submitIcon: {
                    color: titleColor || 'initial',
                  },
                }}
              />

              <div
                className="scrollbar absolute top-0 right-0 w-1 bg-gray-300 rounded transition-all duration-150"
                style={{
                  height: `${thumbHeight}px`,
                  transform: `translateY(${thumbTop}px)`,
                }}
              />
            </div>
          ) : (
            // Poll content using our new ChatPoll component
            <ChatPoll roomId={roomId} />
          )}
        </div>

        {/* Tab Navigation */}
        <div className="mt-4 p-1 bg-gray-800 rounded-md border border-gray-700 flex justify-start items-center">
          <button
            style={{
              backgroundColor: activeTab === 'chat' ? titleColor : undefined,
            }}
            onClick={() => setActiveTab('chat')}
            className={`flex-1 px-3 py-1.5 rounded-sm flex justify-center items-center gap-2 transition-all ${
              activeTab === 'chat'
                ? 'bg-gray-700 shadow-sm text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <ChatCircle
              style={{
                fill: activeTab === 'chat' ? titleColor : undefined,
              }}
              className="w-4 h-4"
            />
            <span className="text-sm font-medium">Chat</span>
          </button>

          <button
            style={{
              backgroundColor: activeTab === 'poll' ? titleColor : undefined,
            }}
            onClick={() => setActiveTab('poll')}
            className={`flex-1 px-3 py-1.5 rounded-sm flex justify-center items-center gap-2 transition-all ${
              activeTab === 'poll'
                ? 'bg-gray-700 shadow-sm text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Question
              style={{
                fill: activeTab === 'poll' ? titleColor : undefined,
              }}
              className="w-4 h-4"
            />
            <span className="text-sm font-medium">Poll</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default LiveChat
