import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'

export interface TabItem {
  label: string
  value: string
}

interface ProfileTabsProps {
  onTabChange: (value: string) => void
  className?: string
  activeTab: string
  tabs: TabItem[]
}

export function CustomTabs({
  tabs,
  activeTab,
  onTabChange,
  className,
}: ProfileTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className={className}>
      <TabsList
        className="grid w-full"
        style={{ gridTemplateColumns: `repeat(${tabs.length}, 1fr)` }}
      >
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}
