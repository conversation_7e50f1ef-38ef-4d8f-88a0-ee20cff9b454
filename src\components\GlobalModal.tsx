import { X } from 'lucide-react'
import { useModal } from '../context/ModalContext'

export const GlobalModal = () => {
  const { isOpen, closeModal, content } = useModal()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[99999999] flex items-center justify-center bg-[hsla(240,10%,4%,0.9)] p-5">
      <div className="w-full max-w-4xl relative flex justify-center">
        <button
          onClick={closeModal}
          className="fixed top-4 right-4 text-white hover:opacity-80"
        >
          <X size={24} />
        </button>
        {content}
      </div>
    </div>
  )
}
