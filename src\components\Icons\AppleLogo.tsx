export const AppleLogo = () => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="AppleLogo">
      <path
        id="Vector"
        d="M14.4561 10.5994C14.4186 10.5137 14.3581 10.4401 14.2811 10.3869C13.2205 9.65813 12.9999 8.415 12.9999 7.5C12.9999 6.39563 13.8418 5.43375 14.3436 4.95813C14.393 4.9114 14.4323 4.8551 14.4592 4.79267C14.4861 4.73023 14.4999 4.66297 14.4999 4.595C14.4999 4.52703 14.4861 4.45977 14.4592 4.39734C14.4323 4.3349 14.393 4.2786 14.3436 4.23188C13.5511 3.48375 12.2386 3 10.9999 3C10.11 3.00068 9.24025 3.26445 8.4999 3.75813C7.63622 3.17898 6.59773 2.91888 5.56303 3.02256C4.52833 3.12625 3.5621 3.58724 2.83052 4.32625C2.39317 4.77275 2.05001 5.30258 1.82143 5.88429C1.59285 6.466 1.4835 7.08771 1.4999 7.7125C1.52464 8.76705 1.75923 9.80611 2.18997 10.769C2.62071 11.7319 3.23897 12.5993 4.00865 13.3206C4.47227 13.7587 5.08644 14.0019 5.72427 14H11.2043C11.5453 14.0006 11.8828 13.9313 12.1958 13.7961C12.5089 13.661 12.7909 13.463 13.0243 13.2144C13.4566 12.7491 13.8307 12.2327 14.138 11.6769C14.5768 10.875 14.5205 10.75 14.4561 10.5994ZM12.2918 12.5331C12.1522 12.6813 11.9836 12.7993 11.7965 12.8796C11.6095 12.9599 11.4079 13.0009 11.2043 13H5.72427C5.34225 13.0012 4.97438 12.8556 4.69677 12.5931C4.02306 11.9624 3.4818 11.2037 3.1046 10.3614C2.72741 9.51913 2.52183 8.61013 2.4999 7.6875C2.48622 7.19729 2.57131 6.70934 2.75008 6.25269C2.92885 5.79603 3.19767 5.38001 3.54052 5.02938C3.86085 4.70212 4.24353 4.44242 4.66596 4.26561C5.08838 4.08881 5.54197 3.99849 5.9999 4H6.04865C6.82756 4.00821 7.58099 4.27858 8.1874 4.7675C8.27608 4.8385 8.38629 4.87719 8.4999 4.87719C8.6135 4.87719 8.72371 4.8385 8.8124 4.7675C9.43181 4.2681 10.2042 3.99709 10.9999 4C11.7944 4.00922 12.5718 4.23271 13.2499 4.64688C12.4374 5.55438 11.9999 6.55125 11.9999 7.5C11.9999 8.98563 12.4774 10.1706 13.3861 10.9563C13.1 11.5323 12.7313 12.0635 12.2918 12.5331ZM8.51427 1.875C8.65294 1.33794 8.96627 0.862253 9.40495 0.522809C9.84362 0.183364 10.3827 -0.000555646 10.9374 1.26101e-06H10.9999C11.1325 1.26101e-06 11.2597 0.0526797 11.3534 0.146448C11.4472 0.240216 11.4999 0.367393 11.4999 0.500001C11.4999 0.632609 11.4472 0.759786 11.3534 0.853555C11.2597 0.947323 11.1325 1 10.9999 1H10.9374C10.6048 0.999972 10.2817 1.11046 10.0188 1.31411C9.75583 1.51775 9.56804 1.803 9.4849 2.125C9.45174 2.25347 9.36892 2.3635 9.25464 2.43089C9.14036 2.49829 9.00398 2.51753 8.87552 2.48438C8.74706 2.45122 8.63702 2.3684 8.56963 2.25412C8.50223 2.13984 8.48299 2.00347 8.51615 1.875H8.51427Z"
        // fill="#09090B"
        fill="currentColor"
      />
    </g>
  </svg>
)
