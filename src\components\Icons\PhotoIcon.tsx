export const PhotoIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.2"
      d="M19.5 6H16.5L15 3.75H9L7.5 6H4.5C4.10218 6 3.72064 6.15804 3.43934 6.43934C3.15804 6.72064 3 7.10218 3 7.5V18C3 18.3978 3.15804 18.7794 3.43934 19.0607C3.72064 19.342 4.10218 19.5 4.5 19.5H19.5C19.8978 19.5 20.2794 19.342 20.5607 19.0607C20.842 18.7794 21 18.3978 21 18V7.5C21 7.10218 20.842 6.72064 20.5607 6.43934C20.2794 6.15804 19.8978 6 19.5 6ZM12 15.75C11.3325 15.75 10.68 15.5521 10.125 15.1812C9.56993 14.8104 9.13735 14.2833 8.88191 13.6666C8.62646 13.0499 8.55962 12.3713 8.68985 11.7166C8.82007 11.0619 9.14151 10.4605 9.61351 9.98851C10.0855 9.51651 10.6869 9.19507 11.3416 9.06485C11.9963 8.93462 12.6749 9.00146 13.2916 9.25691C13.9083 9.51235 14.4354 9.94493 14.8062 10.5C15.1771 11.055 15.375 11.7075 15.375 12.375C15.375 13.2701 15.0194 14.1285 14.3865 14.7615C13.7535 15.3944 12.8951 15.75 12 15.75Z"
      fill="currentColor"
    />
    <path
      d="M19.5 5.25H16.9012L15.6234 3.33375C15.555 3.23114 15.4623 3.147 15.3535 3.08879C15.2448 3.03057 15.1233 3.00007 15 3H9C8.87665 3.00007 8.75522 3.03057 8.64648 3.08879C8.53773 3.147 8.44502 3.23114 8.37656 3.33375L7.09781 5.25H4.5C3.90326 5.25 3.33097 5.48705 2.90901 5.90901C2.48705 6.33097 2.25 6.90326 2.25 7.5V18C2.25 18.5967 2.48705 19.169 2.90901 19.591C3.33097 20.0129 3.90326 20.25 4.5 20.25H19.5C20.0967 20.25 20.669 20.0129 21.091 19.591C21.5129 19.169 21.75 18.5967 21.75 18V7.5C21.75 6.90326 21.5129 6.33097 21.091 5.90901C20.669 5.48705 20.0967 5.25 19.5 5.25ZM20.25 18C20.25 18.1989 20.171 18.3897 20.0303 18.5303C19.8897 18.671 19.6989 18.75 19.5 18.75H4.5C4.30109 18.75 4.11032 18.671 3.96967 18.5303C3.82902 18.3897 3.75 18.1989 3.75 18V7.5C3.75 7.30109 3.82902 7.11032 3.96967 6.96967C4.11032 6.82902 4.30109 6.75 4.5 6.75H7.5C7.62351 6.75008 7.74512 6.71966 7.85405 6.66143C7.96297 6.60321 8.05583 6.51899 8.12438 6.41625L9.40125 4.5H14.5978L15.8756 6.41625C15.9442 6.51899 16.037 6.60321 16.146 6.66143C16.2549 6.71966 16.3765 6.75008 16.5 6.75H19.5C19.6989 6.75 19.8897 6.82902 20.0303 6.96967C20.171 7.11032 20.25 7.30109 20.25 7.5V18ZM12 8.25C11.1842 8.25 10.3866 8.49193 9.70827 8.94519C9.02992 9.39845 8.50121 10.0427 8.189 10.7964C7.87679 11.5502 7.7951 12.3796 7.95426 13.1797C8.11343 13.9799 8.50629 14.7149 9.08318 15.2918C9.66008 15.8687 10.3951 16.2616 11.1953 16.4207C11.9954 16.5799 12.8248 16.4982 13.5786 16.186C14.3323 15.8738 14.9766 15.3451 15.4298 14.6667C15.8831 13.9884 16.125 13.1908 16.125 12.375C16.1238 11.2814 15.6888 10.2329 14.9154 9.45955C14.1421 8.68624 13.0936 8.25124 12 8.25ZM12 15C11.4808 15 10.9733 14.846 10.5416 14.5576C10.1099 14.2692 9.7735 13.8592 9.57482 13.3795C9.37614 12.8999 9.32415 12.3721 9.42544 11.8629C9.52672 11.3537 9.77673 10.886 10.1438 10.5188C10.511 10.1517 10.9787 9.90172 11.4879 9.80044C11.9971 9.69915 12.5249 9.75114 13.0045 9.94982C13.4842 10.1485 13.8942 10.4849 14.1826 10.9166C14.471 11.3483 14.625 11.8558 14.625 12.375C14.625 13.0712 14.3484 13.7389 13.8562 14.2312C13.3639 14.7234 12.6962 15 12 15Z"
      fill="currentColor"
    />
  </svg>
)
