export const Question = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.25 11.25C9.25 11.3983 9.20602 11.5433 9.1236 11.6667C9.04119 11.79 8.92406 11.8861 8.78701 11.9429C8.64997 11.9997 8.49917 12.0145 8.35368 11.9856C8.2082 11.9566 8.07456 11.8852 7.96967 11.7803C7.86478 11.6754 7.79335 11.5418 7.76441 11.3963C7.73548 11.2508 7.75033 11.1 7.80709 10.963C7.86386 10.8259 7.95999 10.7088 8.08333 10.6264C8.20666 10.544 8.35167 10.5 8.5 10.5C8.69892 10.5 8.88968 10.579 9.03033 10.7197C9.17098 10.8603 9.25 11.0511 9.25 11.25ZM8.5 4.5C7.12125 4.5 6 5.50938 6 6.75V7C6 7.13261 6.05268 7.25979 6.14645 7.35355C6.24022 7.44732 6.36739 7.5 6.5 7.5C6.63261 7.5 6.75979 7.44732 6.85356 7.35355C6.94732 7.25979 7 7.13261 7 7V6.75C7 6.0625 7.67313 5.5 8.5 5.5C9.32688 5.5 10 6.0625 10 6.75C10 7.4375 9.32688 8 8.5 8C8.36739 8 8.24022 8.05268 8.14645 8.14645C8.05268 8.24021 8 8.36739 8 8.5V9C8 9.13261 8.05268 9.25979 8.14645 9.35355C8.24022 9.44732 8.36739 9.5 8.5 9.5C8.63261 9.5 8.75979 9.44732 8.85356 9.35355C8.94732 9.25979 9 9.13261 9 9V8.955C10.14 8.74563 11 7.83625 11 6.75C11 5.50938 9.87875 4.5 8.5 4.5ZM15 8C15 9.28558 14.6188 10.5423 13.9046 11.6112C13.1903 12.6801 12.1752 13.5132 10.9874 14.0052C9.79973 14.4972 8.49279 14.6259 7.23192 14.3751C5.97104 14.1243 4.81285 13.5052 3.90381 12.5962C2.99477 11.6872 2.3757 10.529 2.1249 9.26809C1.87409 8.00721 2.00282 6.70028 2.49479 5.51256C2.98676 4.32484 3.81988 3.30968 4.8888 2.59545C5.95772 1.88122 7.21442 1.5 8.5 1.5C10.2234 1.50182 11.8756 2.18722 13.0942 3.40582C14.3128 4.62441 14.9982 6.27665 15 8ZM14 8C14 6.9122 13.6774 5.84883 13.0731 4.94436C12.4687 4.03989 11.6098 3.33494 10.6048 2.91866C9.59977 2.50238 8.4939 2.39346 7.42701 2.60568C6.36011 2.8179 5.3801 3.34172 4.61092 4.11091C3.84173 4.8801 3.3179 5.86011 3.10568 6.927C2.89347 7.9939 3.00238 9.09977 3.41867 10.1048C3.83495 11.1098 4.5399 11.9687 5.44437 12.5731C6.34884 13.1774 7.41221 13.5 8.5 13.5C9.95819 13.4983 11.3562 12.9184 12.3873 11.8873C13.4184 10.8562 13.9983 9.45818 14 8Z"
      fill={props.fill || 'currentColor' || '#FAFAFA'}
    />
  </svg>
)
