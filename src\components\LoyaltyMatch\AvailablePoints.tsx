import { useState, useEffect } from 'react'
import { Loader2, AlertCircle } from 'lucide-react'
import { useUser } from '@/context/UserContext'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'
import { useLoyalty } from '@/context/LoyaltyContext'
import { t } from '@/helpers'

interface AvailablePointsProps {
  className?: string
  labelClassName?: string
  valueClassName?: string
}

export function AvailablePoints({
  className = 'flex items-center gap-2',
  labelClassName = 'text-lg font-semibold',
  valueClassName = 'text-2xl font-bold',
}: AvailablePointsProps) {
  const { userData } = useUser()
  const { points: contextPoints, isLoading: contextLoading } = useLoyalty()
  const userId = userData?.['Custom:User:Id'] || userData?.['Content:Id']

  const [points, setPoints] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  // Log userID for debugging
  useEffect(() => {
    console.log('🔹 AvailablePoints - userId:', userId)
  }, [userId])

  useEffect(() => {
    const fetchPoints = async () => {
      // Skip if we already have points from context
      if (contextPoints > 0) {
        setPoints(contextPoints)
        return
      }

      if (!userId) return

      setIsLoading(true)
      setError(null)

      try {
        const loyaltyService = new LoyaltyMatchService()
        const url = `${loyaltyService.proxyUrl}/currentpoints/userId/${userId}`

        console.log('Fetching points balance from:', url)
        const response = await fetch(url)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`Failed to fetch points: ${errorText}`)
        }

        const data = await response.json()
        console.log('Points API response:', data)

        // Handle different response formats
        let pointsValue = 0
        if (data.total !== undefined) {
          pointsValue = data.total
        } else if (data.totalCurrentPoints !== undefined) {
          pointsValue = data.totalCurrentPoints
        } else if (data.points !== undefined) {
          pointsValue = data.points
        }

        setPoints(pointsValue)
      } catch (err) {
        console.error('Failed to fetch points:', err)
        setError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPoints()
  }, [userId, contextPoints])

  // Use context values if they're loaded, otherwise use direct API values
  const displayPoints = points !== null ? points : contextPoints
  const showLoading = isLoading || (contextLoading && points === null)

  // Log the final points value that will be displayed
  useEffect(() => {
    console.log('🔹 AvailablePoints - displayPoints:', displayPoints)
    console.log('🔹 AvailablePoints - context vs direct:', {
      contextPoints,
      directPoints: points,
      usingSource: points !== null ? 'direct API' : 'context',
    })
  }, [displayPoints, contextPoints, points])

  // Display error state if we have an error and no points
  if (error && !displayPoints) {
    return (
      <div className={className}>
        <AlertCircle className="h-4 w-4 text-destructive" />
        <span className="text-sm text-destructive">Error loading points</span>
      </div>
    )
  }

  return (
    <div className={className}>
      {showLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <>
          <div
            className={labelClassName}
            dangerouslySetInnerHTML={{
              __html: `${t('APP_LOYALTY_POINTS_BALANCE_TITLE')}:`,
            }}
          />
          <span className={valueClassName}>{displayPoints}</span>
        </>
      )}
    </div>
  )
}
