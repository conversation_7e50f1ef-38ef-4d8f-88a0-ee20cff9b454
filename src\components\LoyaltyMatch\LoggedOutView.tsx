import { Card } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { IntroSectionLoggedOut } from './Sections/IntroSectionLoggedOut'
import { ConnectEngageRewardsSection } from './Sections/ConnectEngageRewardsSection'
import { BadgesSection } from './Sections/BadgesSectionLoggedOut'
import { PointsSectionLoggedOut } from './Sections/PointsSectionLoggedOut'
import { t } from '@/helpers'

export function LoggedOutView() {
  const { setIsMenuOpen, colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor

  return (
    <div className="flex flex-col space-y-4 w-full pb-4">
      <div className="flex justify-between items-top">
        <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
          <button
            style={{ color: titleColor || undefined }}
            className="flex flex-col justify-center md:hidden"
            onClick={() => setIsMenuOpen(true)}
          >
            <MobileMenuIcon />
          </button>
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
          >
            {t('APP_LOYALTY_TITLE')}
          </h1>
        </div>
      </div>
      <IntroSectionLoggedOut />
      <ConnectEngageRewardsSection />
      <Card
        style={{ backgroundColor: backgroundColor || undefined }}
        className="flex flex-col border-none bg-sidebar-hub-background mb-8"
      >
        <BadgesSection />
        <PointsSectionLoggedOut />
      </Card>
    </div>
  )
}
