import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Card, CardHeader, CardContent } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

export function LoyaltyFAQ() {
  const faqCount = 3 // ← change to how many entries you have
  const faqIndices = Array.from({ length: faqCount }, (_, i) => i + 1)

  const { colorsInfo, config } = useAppContext()

  const secondaryText = colorsInfo?.secondaryText

  return (
    <Card className="flex flex-col border-none bg-transparent mb-8">
      <CardHeader className="flex-shrink-0 p-0 py-6">
        <h3
          style={{ color: secondaryText || undefined }}
          className="text-3xl font-semibold text-base-foreground"
        >
          {t('APP_LOYALTY_FAQ_TITLE')}
        </h3>
      </CardHeader>
      <CardContent className="p-0">
        <div className="self-stretch w-full inline-flex flex-col justify-start items-start gap-6">
          <Accordion type="single" collapsible className="w-full">
            {faqIndices.map((n) => (
              <AccordionItem
                key={n}
                value={`faq-${n}`}
                className="px-3 border border-zinc-600 rounded-xl outline outline-1 outline-offset-[-1px] outline-zinc-600"
                style={{ color: secondaryText || undefined }}
              >
                <AccordionTrigger className="py-4 text-xl hover:no-underline">
                  {t(`APP_FAQ_TITLE_${n}`)}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p
                    className="text-base-foreground text-base"
                    dangerouslySetInnerHTML={{
                      __html:
                        config?.name === 'Walk off the Earth' && n === 2
                          ? t(`APP_FAQ_WOTE_CONTENT_${n}`)
                          : t(`APP_FAQ_CONTENT_${n}`),
                    }}
                  />
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </CardContent>
    </Card>
  )
}
