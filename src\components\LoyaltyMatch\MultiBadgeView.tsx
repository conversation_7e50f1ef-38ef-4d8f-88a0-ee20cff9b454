import { Card, CardHeader, CardContent } from '@/components/ui/card'
import RewardBadges from '@/components/LoyaltyMatch/RewardBadges'
import { PointsHistory } from '@/components/LoyaltyMatch/PointsHistory'
import { AvailablePoints } from '@/components/LoyaltyMatch/AvailablePoints'
import { LoyaltyFAQ } from '@/components/LoyaltyMatch/LoyaltyFAQ'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

interface MultiBadgeViewProps {
  userId: string
}

export function MultiBadgeView({ userId }: MultiBadgeViewProps) {
  const { setIsMenuOpen, colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor

  return (
    <div className="flex flex-col space-y-4 w-full pb-4">
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <h1
          style={{ color: titleColor || undefined }}
          className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
        >
          {t('APP_LOYALTY_TITLE')}
        </h1>
      </div>

      <Card
        style={{ backgroundColor: backgroundColor || undefined }}
        className="flex flex-col border-none bg-sidebar-hub-background mb-8"
      >
        <CardHeader className="flex-shrink-0 p-0 mb-5">
          <h3
            style={{ color: titleColor || undefined }}
            className="text-2xl font-bold"
          >
            {t('APP_LOYALTY_YOUR_BADGES_TITLE')}
          </h3>
        </CardHeader>
        <CardContent className="p-0">
          <RewardBadges displayMode="stacked" />
        </CardContent>
      </Card>

      <div>
        <AvailablePoints />
      </div>

      <div>{userId && <PointsHistory userId={userId} />}</div>

      <LoyaltyFAQ />
    </div>
  )
}
