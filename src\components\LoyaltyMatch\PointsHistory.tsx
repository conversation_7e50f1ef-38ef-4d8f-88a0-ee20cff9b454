import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import { format } from 'date-fns'
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'
import { t } from '@/helpers'
import { useAppContext } from '@/context/AppContext'

type Transaction = {
  id: string
  timestamp: Date
  points: number
  type: 'earn' | 'redeem'
  activityName: string
  balance: number
}

// Create a helper to store transactions in localStorage by userId
const getStoredTransactions = (userId: string): Transaction[] => {
  try {
    const stored = localStorage.getItem(`transactions_${userId}`)
    return stored ? JSON.parse(stored) : []
  } catch (e) {
    return []
  }
}

const storeTransactions = (userId: string, transactions: Transaction[]) => {
  try {
    localStorage.setItem(`transactions_${userId}`, JSON.stringify(transactions))
  } catch (e) {
    console.error('Could not store transactions', e)
  }
}

// Define a type for the ref
export type PointsHistoryRef = {
  addTransaction: (points: number, activityName: string) => void
}

// Use forwardRef to expose methods to parent
export const PointsHistory = forwardRef<PointsHistoryRef, { userId: string }>(
  ({ userId }, ref) => {
    const { colorsInfo } = useAppContext()

    const secondaryText = colorsInfo?.secondaryText

    const [transactions, setTransactions] = useState<Transaction[]>([])
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<Error | null>(null)

    // Pagination state
    const [currentPage, setCurrentPage] = useState(0)
    const itemsPerPage = 5

    useEffect(() => {
      if (!userId) return

      const fetchPointsHistory = async () => {
        setIsLoading(true)
        setError(null)

        try {
          const loyaltyService = new LoyaltyMatchService()

          // Try using the memberpointsactivities endpoint
          const activitiesUrl = `${loyaltyService.proxyUrl}/memberpointsactivities/userId/${userId}`
          // console.log('Fetching points history from:', activitiesUrl)

          const activitiesResponse = await fetch(activitiesUrl)

          if (activitiesResponse.ok) {
            const activitiesData = await activitiesResponse.json()
            // console.log('Points activities data:', activitiesData)

            // Check what data structure we're getting
            // console.log('Response structure:', Object.keys(activitiesData))

            // Format the activities into transactions
            if (
              activitiesData &&
              (activitiesData.activities ||
                activitiesData.apiMemberPointsActivities)
            ) {
              const activities =
                activitiesData.activities ||
                activitiesData.apiMemberPointsActivities ||
                []

              if (activities.length > 0) {
                // console.log(
                //   'First activity structure:',
                //   Object.keys(activities[0]),
                // )

                // Get the API data and format it
                const apiTransactions = activities.map((activity: any) => {
                  return {
                    id:
                      activity.id ||
                      activity.pointsActivityId ||
                      `api-tx-${Date.now()}-${Math.random()}`,
                    timestamp: new Date(
                      activity.transactionDate || activity.date || new Date(),
                    ),
                    points: activity.points || 0,
                    type:
                      activity.points >= 0 ||
                      activity.pointsActivityTypeId !== 3
                        ? 'earn'
                        : 'redeem',
                    activityName:
                      activity.transactionEnglishactivityName ||
                      activity.activityName ||
                      'Points transaction',
                    balance: activity.runningBalance || activity.balance || 0,
                  }
                })

                // Sort by date, newest first
                apiTransactions.sort(
                  (a: any, b: any) =>
                    new Date(b.timestamp).getTime() -
                    new Date(a.timestamp).getTime(),
                )

                setTransactions(apiTransactions)
                setIsLoading(false)
                return // Exit early as we have data
              }
            }
          }

          // Fall back to current balance method if the activity endpoint fails
          const pointsUrl = `${loyaltyService.proxyUrl}/currentpoints/userId/${userId}`
          const pointsResponse = await fetch(pointsUrl)

          if (!pointsResponse.ok) {
            throw new Error('Failed to fetch points balance')
          }

          const pointsData = await pointsResponse.json()
          const currentBalance = pointsData.total || 0

          // Use the currentBalance in the stored transactions
          let storedTransactions = getStoredTransactions(userId)

          // Add the current balance if no transactions exist yet
          if (storedTransactions.length === 0) {
            storedTransactions = [
              {
                id: `tx-${Date.now()}`,
                timestamp: new Date(),
                points: currentBalance,
                type: 'earn',
                activityName: 'Current balance',
                balance: currentBalance,
              },
            ]
            storeTransactions(userId, storedTransactions)
          }

          setTransactions(storedTransactions)
        } catch (err) {
          console.error('Failed to fetch points history:', err)
          setError(err as Error)
        } finally {
          setIsLoading(false)
        }
      }

      fetchPointsHistory()
    }, [userId])

    // Expose the function to parent components
    useImperativeHandle(ref, () => ({
      addTransaction: (points: number, activityName: string) => {
        const newBalance =
          transactions.length > 0 ? transactions[0].balance + points : points

        const newTransaction: Transaction = {
          id: `tx-${Date.now()}`,
          timestamp: new Date(),
          points: points,
          type: 'earn',
          activityName: activityName || 'Points awarded',
          balance: newBalance,
        }

        const updatedTransactions = [newTransaction, ...transactions]
        setTransactions(updatedTransactions)
        storeTransactions(userId, updatedTransactions)
        return updatedTransactions
      },
    }))

    // Pagination logic
    const totalPages = Math.ceil(transactions.length / itemsPerPage)
    const paginatedTransactions = transactions.slice(
      currentPage * itemsPerPage,
      (currentPage + 1) * itemsPerPage,
    )

    const goToNextPage = () => {
      if (currentPage < totalPages - 1) {
        setCurrentPage(currentPage + 1)
      }
    }

    const goToPreviousPage = () => {
      if (currentPage > 0) {
        setCurrentPage(currentPage - 1)
      }
    }

    // Reset pagination when user changes
    useEffect(() => {
      setCurrentPage(0)
    }, [userId])

    if (isLoading) {
      return (
        <div className="w-full flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      )
    }

    if (error) {
      return (
        <div className="w-full flex items-center justify-center text-destructive py-4">
          Failed to load points history
        </div>
      )
    }

    return (
      <div className="self-stretch w-full px-3 rounded-xl outline outline-1 outline-zinc-600 outline-offset-[-1px] outline-base-card inline-flex flex-col justify-start items-start gap-6">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="activity-history" className="border-b-0">
            <AccordionTrigger
              style={{ color: secondaryText || undefined }}
              className="py-4 text-xl hover:no-underline"
            >
              {t('APP_LOYALTY_POINT_HISTORY_TITLE')}
            </AccordionTrigger>
            <AccordionContent>
              <div className="w-full rounded-lg overflow-x-auto outline outline-1 outline-zinc-600 outline-offset-[-1px] outline-base-card">
                <Table>
                  <TableHeader>
                    <TableRow className="border-zinc-600 hover:bg-transparent">
                      <TableHead className="p-2">Date</TableHead>
                      <TableHead className="p-2">Action</TableHead>
                      <TableHead className="p-2 text-right">Points</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedTransactions.map((entry) => (
                      <TableRow
                        key={entry.id}
                        className="border-zinc-600 hover:bg-transparent"
                      >
                        <TableCell
                          style={{ color: secondaryText || undefined }}
                          className="font-medium whitespace-nowrap p-2"
                        >
                          {format(new Date(entry.timestamp), 'MMMM d, yyyy')}
                        </TableCell>
                        <TableCell
                          style={{ color: secondaryText || undefined }}
                          className="whitespace-nowrap p-2"
                        >
                          {entry.activityName}
                        </TableCell>
                        <TableCell
                          className={cn('whitespace-nowrap p-2 text-right')}
                          style={{ color: secondaryText || undefined }}
                        >
                          {entry.type === 'earn'
                            ? `+${entry.points}`
                            : `-${entry.points}`}
                        </TableCell>
                      </TableRow>
                    ))}
                    {(!transactions || transactions.length === 0) && (
                      <TableRow className="border-zinc-600 hover:bg-transparent">
                        <TableCell
                          colSpan={4}
                          className="text-center py-4 text-muted-foreground"
                          style={{ color: secondaryText || undefined }}
                        >
                          No points history for this user
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>

                {/* Pagination controls - only show if we have transactions */}
                {transactions.length > 0 && totalPages > 1 && (
                  <div className="flex items-center justify-end gap-2 p-2 border-t border-zinc-600">
                    <div className="text-xs text-muted-foreground">
                      Page {currentPage + 1} of {totalPages}
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6 rounded-md border-zinc-600"
                      onClick={goToPreviousPage}
                      disabled={currentPage === 0}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6 rounded-md border-zinc-600"
                      onClick={goToNextPage}
                      disabled={currentPage >= totalPages - 1}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    )
  },
)
