import { useEffect, useState } from 'react'
import { useUser } from '@/context/UserContext'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'
import { Loader2 } from 'lucide-react'
import { useAppContext } from '@/context/AppContext'

interface Badge {
  badgeId: string | number
  badgeTitle?: string
  badgeName?: string
  title?: string
  badgeDescription?: string
  description?: string
  badgeLargeImageUrl?: string
  badgeSmallImageUrl?: string
  badgeImageUrl?: string
  imageUrl?: string
  dateEarned?: string
}

// Define display mode options
type DisplayMode = 'full' | 'image-only' | 'compact' | 'stacked'

interface RewardBadgesProps {
  displayMode?: DisplayMode
  highlightFirstBadge?: boolean
  className?: string
}

export default function RewardBadges({
  displayMode = 'full',
  highlightFirstBadge = false,
  className = '',
}: RewardBadgesProps) {
  const { userData } = useUser()
  const userId = userData?.['Custom:User:Id'] || userData?.['Content:Id']

  const [badges, setBadges] = useState<Badge[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const { colorsInfo } = useAppContext()

  useEffect(() => {
    if (!userId) return

    const fetchUserBadges = async () => {
      try {
        setIsLoading(true)
        const loyaltyService = new LoyaltyMatchService()
        const url = `${loyaltyService.proxyUrl}/badgesbymember/userId/${userId}`

        console.log('Fetching user badges from:', url)
        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`Failed to fetch badges: ${await response.text()}`)
        }

        const data = await response.json()
        console.log('User badges response:', data)

        // Extract badges from the response
        const userBadges = data.apiBadges || []

        // Filter to only include earned badges (with dateEarned)
        const earnedBadges = userBadges.filter(
          (badge: Badge) => badge.dateEarned,
        )

        setBadges(earnedBadges)
      } catch (err) {
        console.error('Failed to fetch user badges:', err)
        setError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserBadges()
  }, [userId])

  const titleColor = colorsInfo?.secondaryBackgroundColor

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-4">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-destructive text-center py-4">
        Unable to load badges
      </div>
    )
  }

  if (badges.length === 0) {
    return (
      <div className="text-muted-foreground text-center py-4">
        No badges earned yet. Complete activities to earn badges!
      </div>
    )
  }

  // Image-only version (for SingleBadgeView)
  if (displayMode === 'image-only' && badges.length > 0) {
    const badge = badges[0] // Get the first badge
    return (
      <div>
        <div
          className={`${highlightFirstBadge ? 'w-32 h-32 p-4 border-4 border-primary rounded-full' : 'w-24 h-24'} flex items-center justify-center`}
        >
          {badge.badgeLargeImageUrl || badge.badgeImageUrl || badge.imageUrl ? (
            <img
              src={
                badge.badgeLargeImageUrl ||
                badge.badgeImageUrl ||
                badge.imageUrl
              }
              alt={
                badge.badgeTitle ||
                badge.title ||
                badge.badgeName ||
                `Badge ${badge.badgeId}`
              }
              className="max-w-full max-h-full object-contain"
              onError={(e) => {
                // If image fails to load, show a fallback
                ;(e.currentTarget as HTMLImageElement).style.display = 'none'
                ;(e.currentTarget.parentNode as HTMLElement).innerHTML =
                  `<div class="w-full h-full bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">${badge.badgeId}</div>`
              }}
            />
          ) : (
            <div className="w-full h-full bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">
              {badge.badgeId}
            </div>
          )}
        </div>
      </div>
    )
  }

  // Compact version (could be useful in the future)
  if (displayMode === 'compact') {
    return (
      <div className={`flex flex-wrap gap-2 justify-center ${className}`}>
        {badges.map((badge) => (
          <div
            key={badge.badgeId}
            className="w-16 h-16 flex items-center justify-center"
          >
            {badge.badgeSmallImageUrl ||
            badge.badgeImageUrl ||
            badge.imageUrl ? (
              <img
                src={
                  badge.badgeSmallImageUrl ||
                  badge.badgeImageUrl ||
                  badge.imageUrl
                }
                alt={
                  badge.badgeTitle ||
                  badge.title ||
                  badge.badgeName ||
                  `Badge ${badge.badgeId}`
                }
                className="max-w-full max-h-full object-contain"
                title={
                  badge.badgeTitle ||
                  badge.title ||
                  badge.badgeName ||
                  `Badge ${badge.badgeId}`
                }
              />
            ) : (
              <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center text-primary text-xs font-bold">
                {badge.badgeId}
              </div>
            )}
          </div>
        ))}
      </div>
    )
  }

  // Stacked version (for MultiBadgeView)
  if (displayMode === 'stacked') {
    return (
      <div className={`flex flex-col gap-4 ${className}`}>
        {badges.map((badge) => (
          <div
            key={badge.badgeId}
            className="self-stretch p-2 rounded-xl outline outline-1 outline-offset-[-2px] outline-zinc-600 outline-base-card inline-flex justify-start items-center gap-4"
          >
            <div className="flex-shrink-0">
              {badge.badgeLargeImageUrl ||
              badge.badgeImageUrl ||
              badge.imageUrl ? (
                <img
                  src={
                    badge.badgeLargeImageUrl ||
                    badge.badgeImageUrl ||
                    badge.imageUrl
                  }
                  alt={
                    badge.badgeTitle ||
                    badge.title ||
                    badge.badgeName ||
                    `Badge ${badge.badgeId}`
                  }
                  className="w-24 h-24 object-contain"
                  onError={(e) => {
                    // If image fails to load, show a fallback
                    ;(e.currentTarget as HTMLImageElement).style.display =
                      'none'
                    ;(e.currentTarget.parentNode as HTMLElement).innerHTML =
                      `<div class="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">${badge.badgeId}</div>`
                  }}
                />
              ) : (
                <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">
                  {badge.badgeId}
                </div>
              )}
            </div>

            <div className="flex flex-col justify-start gap-1">
              <div
                style={{ color: titleColor || undefined }}
                className="flex-1 justify-start text-base-foreground text-xl font-semibold leading-7"
              >
                {badge.badgeTitle ||
                  badge.title ||
                  badge.badgeName ||
                  `Badge ${badge.badgeId}`}
              </div>

              {(badge.badgeDescription || badge.description) && (
                <p className="text-sm text-muted-foreground">
                  {badge.badgeDescription || badge.description}
                </p>
              )}

              {/* {badge.dateEarned && (
                <span className="text-xs text-primary">
                  Earned: {new Date(badge.dateEarned).toLocaleDateString()}
                </span>
              )} */}
            </div>
          </div>
        ))}
      </div>
    )
  }

  // Full version (default, for MultiBadgeView)
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 ${className}`}>
      {badges.map((badge, index) => (
        <div
          key={badge.badgeId}
          className={`flex flex-col items-center bg-background/50 rounded-lg p-3 border-2 ${
            highlightFirstBadge && index === 0
              ? 'border-primary'
              : 'border-primary/10 hover:border-primary/30'
          } transition-colors`}
        >
          {/* Badge Image */}
          <div className="w-20 h-20 mb-2 flex items-center justify-center">
            {badge.badgeLargeImageUrl ||
            badge.badgeImageUrl ||
            badge.imageUrl ? (
              <img
                src={
                  badge.badgeLargeImageUrl ||
                  badge.badgeImageUrl ||
                  badge.imageUrl
                }
                alt={
                  badge.badgeTitle ||
                  badge.title ||
                  badge.badgeName ||
                  `Badge ${badge.badgeId}`
                }
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  // If image fails to load, show a fallback
                  ;(e.currentTarget as HTMLImageElement).style.display = 'none'
                  ;(e.currentTarget.parentNode as HTMLElement).innerHTML =
                    `<div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">${badge.badgeId}</div>`
                }}
              />
            ) : (
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">
                {badge.badgeId}
              </div>
            )}
          </div>

          {/* Badge Title */}
          <h4 className="font-semibold text-center">
            {badge.badgeTitle ||
              badge.title ||
              badge.badgeName ||
              `Badge ${badge.badgeId}`}
          </h4>

          {/* Badge Description - optional */}
          {(badge.badgeDescription || badge.description) && (
            <p className="text-xs text-muted-foreground text-center mt-1">
              {badge.badgeDescription || badge.description}
            </p>
          )}

          {/* Date Earned - optional */}
          {badge.dateEarned && (
            <span className="text-xs text-primary mt-1">
              Earned: {new Date(badge.dateEarned).toLocaleDateString()}
            </span>
          )}
        </div>
      ))}
    </div>
  )
}
