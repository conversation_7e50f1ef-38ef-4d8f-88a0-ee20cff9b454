import { <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

export function BadgesSection() {
  const { colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <>
      <CardHeader className="p-0 mb-5">
        <h2 className="text-3xl font-semibold">
          <div
            dangerouslySetInnerHTML={{
              __html: t('APP_LOYALTY_SIGNEDOUT_SECTION2_HEADER'),
            }}
            style={{ color: titleColor || undefined }}
          />
        </h2>
      </CardHeader>
      <CardContent className="p-0">
        <p className="text-lg">
          <div
            dangerouslySetInnerHTML={{
              __html: t('APP_LOYALTY_SIGNEDOUT_SECTION2_BODY'),
            }}
            style={{ color: secondaryText || undefined }}
          />
        </p>
      </CardContent>
    </>
  )
}

export default BadgesSection
