import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ShoppingBagIcon, CircleIcon, ChatIcon } from '@/components/Icons'
import { t } from '@/helpers'
import { useAppContext } from '@/context/AppContext'

export function ButtonSectionSingleBadge() {
  const { setActiveTab, colorsInfo } = useAppContext() // Use the tab-based navigation system

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const fontColor = colorsInfo?.fontColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <>
      <Card className="flex flex-col border-none bg-transparent mb-8">
        <CardContent className="py-4 px-0">
          <div className="flex flex-col gap-6">
            <div className="pb-2">
              <h3 className="text-3xl font-semibold text-base-foreground leading-9">
                <div
                  dangerouslySetInnerHTML={{
                    __html: t('APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_TITLE'),
                  }}
                  style={{ color: secondaryText || undefined }}
                />
              </h3>
            </div>

            <p className="text-lg text-base-foreground leading-7">
              <div
                dangerouslySetInnerHTML={{
                  __html: t('APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_CONTENT'),
                }}
                style={{ color: secondaryText || undefined }}
              />
            </p>

            <div className="flex flex-col gap-6">
              {/* Make a Purchase section */}
              <div className="flex flex-col justify-between gap-3">
                <div className="flex justify-center items-center">
                  <img
                    src="https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_18_Small.png"
                    alt="Loyalty Badge"
                    className="h-24 w-24"
                  />
                </div>
                <div>
                  <p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: t(
                          'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON1_CONTENT',
                        ),
                      }}
                      style={{ color: secondaryText || undefined }}
                    />
                  </p>
                </div>
                <div className="flex justify-center">
                  <Button
                    className="bg-primary text-zinc-900 hover:bg-primary px-4 py-2 rounded-md justify-center items-center gap-2 inline-flex font-medium w-48"
                    style={{
                      backgroundColor: titleColor || undefined,
                      color: fontColor || undefined,
                    }}
                    onClick={() => setActiveTab('shopping')} // Use tab-based navigation
                  >
                    <ShoppingBagIcon />
                    {t(
                      'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON1_BUTTONTEXT',
                    )}
                  </Button>
                </div>
              </div>

              {/* Watch & Stream section */}
              <div className="flex flex-col justify-between gap-3">
                <div className="flex justify-center items-center">
                  <img
                    src="https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_6_Small.png"
                    alt="Loyalty Badge"
                    className="h-24 w-24"
                  />
                </div>
                <div>
                  <p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: t(
                          'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON2_CONTENT',
                        ),
                      }}
                      style={{ color: secondaryText || undefined }}
                    />
                  </p>
                </div>
                <div className="flex justify-center">
                  <Button
                    className="bg-primary text-zinc-900 hover:bg-primary px-4 py-2 rounded-md justify-center items-center gap-2 inline-flex font-medium w-48"
                    style={{
                      backgroundColor: titleColor || undefined,
                      color: fontColor || undefined,
                    }}
                    onClick={() => setActiveTab('virtual-events')} // Use tab-based navigation
                  >
                    <CircleIcon />
                    {t(
                      'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON2_BUTTONTEXT',
                    )}
                  </Button>
                </div>
              </div>

              {/* Engage & Connect section */}
              <div className="flex flex-col justify-between gap-3">
                <div className="flex justify-center items-center">
                  <img
                    src="https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_25_Small.png"
                    alt="Loyalty Badge"
                    className="h-24 w-24"
                  />
                </div>
                <div>
                  <p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: t(
                          'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON3_CONTENT',
                        ),
                      }}
                      style={{ color: secondaryText || undefined }}
                    />
                  </p>
                </div>
                <div className="flex justify-center">
                  <Button
                    className="bg-primary text-zinc-900 hover:bg-primary px-4 py-2 rounded-md justify-center items-center gap-2 inline-flex font-medium w-48"
                    style={{
                      backgroundColor: titleColor || undefined,
                      color: fontColor || undefined,
                    }}
                    onClick={() => setActiveTab('chat')} // Use tab-based navigation
                  >
                    <ChatIcon />
                    {t(
                      'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION2_BUTTON3_BUTTONTEXT',
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}
