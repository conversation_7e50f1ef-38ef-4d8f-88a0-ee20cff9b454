import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAppContext } from '@/context/AppContext'
import { Tab } from '@/types/navigation' // Ensure Tab type is imported
import { t } from '@/helpers'

export function ConnectEngageRewardsSection() {
  const { setActiveTab, colorsInfo } = useAppContext()

  const handleNavigation = (tab: Tab) => {
    // Updated to use Tab type
    setActiveTab(tab)
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor
  const fontColor = colorsInfo?.fontColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <div className="self-stretch p-5 rounded-xl outline outline-1 outline-offset-[-1px] outline-zinc-400 inline-flex flex-col justify-start items-start gap-3">
      <Card
        style={{ backgroundColor: backgroundColor || undefined }}
        className="flex flex-col border-none bg-sidebar-hub-background"
      >
        <CardHeader className="flex-shrink-0 p-0 mb-5">
          <h3
            style={{ color: titleColor || undefined }}
            className="text-2xl font-semibold text-accent-3"
          >
            {t('APP_LOYALTY_SIGNEDOUT_JOINNOW_HEADER')}
          </h3>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex flex-col gap-4">
            <div
              dangerouslySetInnerHTML={{
                __html: t('APP_LOYALTY_SIGNEDOUT_BONNIE_INTRO'),
              }}
              style={{ color: secondaryText || undefined }}
            />
            <h3
              style={{ color: titleColor || undefined }}
              className="text-accent-3 text-xl font-semibold"
            >
              {t('APP_LOYALTY_SIGNEDOUT_PERKS')}
            </h3>
            <div
              dangerouslySetInnerHTML={{
                __html: t('APP_LOYALTY_SIGNEDOUT_PERKS_LIST'),
              }}
              style={{ color: secondaryText || undefined }}
            />
          </div>
          <Button
            variant="default"
            className="w-full mt-4"
            onClick={() => handleNavigation('profile')} // Ensure 'loyalty' is a valid Tab value
            style={{
              backgroundColor: titleColor || undefined,
              color: fontColor || undefined,
            }}
          >
            {t('APP_LOYALTY_SIGNEDOUT_PERKS_BUTTON_TEXT')}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

export default ConnectEngageRewardsSection
