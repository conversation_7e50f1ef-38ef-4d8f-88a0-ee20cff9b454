import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

export function IntroSectionLoggedOut() {
  const { colorsInfo } = useAppContext()

  const secondaryText = colorsInfo?.secondaryText

  return (
    <>
      <p>
        <div
          dangerouslySetInnerHTML={{
            __html: t('APP_LOYALTY_SIGNEDOUT_SECTION1_BODY'),
          }}
          style={{ color: secondaryText || undefined }}
        />
      </p>
    </>
  )
}
