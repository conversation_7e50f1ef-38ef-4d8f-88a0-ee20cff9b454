import { t } from '@/helpers'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'

export function IntroSectionSingleBadge() {
  const { colorsInfo } = useAppContext()

  const secondaryText = colorsInfo?.secondaryText

  return (
    <>
      <Card className="flex flex-col bg-transparent border-none mb-8">
        <CardHeader className="flex-shrink-0 p-0 mb-5">
          <h3
            style={{ color: secondaryText || undefined }}
            className="text-2xl font-bold"
          >
            <div
              dangerouslySetInnerHTML={{
                __html: t('APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION1_HEADING'),
              }}
            />
          </h3>
        </CardHeader>
        <CardContent className="p-0">
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <img
              src="https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_4_Small.png"
              alt="Loyalty Badge"
              className="w-24 h-24"
            />
            <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
              <div className="pb-2 inline-flex justify-start items-center gap-2">
                <div className="justify-start text-3xl font-semibold leading-9">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: t('APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION1_TITLE'),
                    }}
                    style={{ color: secondaryText || undefined }}
                  />
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-2">
                <div className="flex-1 justify-start">
                  <span className="text-base font-normal leading-7">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: t(
                          'APP_LOYALTY_SIGNEDIN_ONEBADGE_SECTION1_CONTENT',
                        ),
                      }}
                      style={{ color: secondaryText || undefined }}
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}
