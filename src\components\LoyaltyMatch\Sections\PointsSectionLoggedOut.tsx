import { CardContent } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'
import { t } from '@/helpers'

const items = [
  {
    img: 'https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_4_Small.png',
    header: 'APP_LOYALTY_SIGNEDOUT_POINTS1_HEADER' as const,
    desc: 'APP_LOYALTY_SIGNEDOUT_POINTS1_DESCRIPTION' as const,
  },
  {
    img: 'https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_9_Small.png',
    header: 'APP_LOYALTY_SIGNEDOUT_POINTS2_HEADER' as const,
    desc: 'APP_LOYALTY_SIGNEDOUT_POINTS2_DESCRIPTION' as const,
  },
  {
    img: 'https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_22_Small.png',
    header: 'APP_LOYALTY_SIGNEDOUT_POINTS3_HEADER' as const,
    desc: 'APP_LOYALTY_SIGNEDOUT_POINTS3_DESCRIPTION' as const,
  },
  {
    img: 'https://official.myloyaltymatch.com/od-cust/10039/images/badges/b_5_Small.png',
    header: 'APP_LOYALTY_SIGNEDOUT_POINTS4_HEADER' as const,
    desc: 'APP_LOYALTY_SIGNEDOUT_POINTS4_DESCRIPTION' as const,
  },
]

export function PointsSectionLoggedOut() {
  const { colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <CardContent className="p-0">
      <h2 className="text-3xl font-semibold mt-0">
        <div
          dangerouslySetInnerHTML={{
            __html: t('APP_LOYALTY_SIGNEDOUT_SECTION3_HEADER'),
          }}
          style={{ color: titleColor || undefined }}
        />
      </h2>
      {items.map(({ img, header, desc }) => (
        <div key={header} className="flex gap-4 mt-6">
          <img src={img} alt="" className="w-24 h-24" />
          <div className="flex flex-col justify-center">
            <p
              className="text-lg font-semibold"
              dangerouslySetInnerHTML={{ __html: t(header) }}
              style={{ color: secondaryText || undefined }}
            />
            <p
              className=""
              dangerouslySetInnerHTML={{ __html: t(desc) }}
              style={{ color: secondaryText || undefined }}
            />
          </div>
        </div>
      ))}
    </CardContent>
  )
}
