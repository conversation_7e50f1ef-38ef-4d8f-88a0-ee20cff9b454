import { PointsHistory } from '@/components/LoyaltyMatch/PointsHistory'
import { useAppContext } from '@/context/AppContext'
import { LoyaltyFAQ } from '@/components/LoyaltyMatch/LoyaltyFAQ'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { t } from '@/helpers'
import { IntroSectionSingleBadge } from './Sections/IntroSectionSingleBadge'
import { ButtonSectionSingleBadge } from './Sections/ButtonSectionSingleBadge'

interface SingleBadgeViewProps {
  userId: string
}

export function SingleBadgeView({ userId }: SingleBadgeViewProps) {
  const { setIsMenuOpen, colorsInfo, config } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="flex flex-col space-y-4 w-full pb-4">
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <h1
          style={{ color: titleColor || undefined }}
          className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
        >
          {t('APP_LOYALTY_TITLE')}
        </h1>
      </div>

      <IntroSectionSingleBadge />

      <div>{userId && <PointsHistory userId={userId} />}</div>

      {config?.name !== 'Walk off the Earth' && <ButtonSectionSingleBadge />}

      <LoyaltyFAQ />
    </div>
  )
}
