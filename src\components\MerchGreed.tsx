import { useAppContext } from '@/context/AppContext'
import Price from './Shop/Price'
import { useUser } from '@/context/UserContext'

interface MerchGridProps {
  readonly items: {
    id: string
    image: string
    title: string
    price?: {
      amount: string
      currencyCode: string
    }
    productType?: string
  }[]
  readonly onProductSelect: (id: string) => void
}

export default function MerchGrid({ items, onProductSelect }: MerchGridProps) {
  const { setActiveTab } = useAppContext()
  const { isUserAuthenticated } = useUser()

  return (
    <div className="w-full overflow-hidden">
      <div className="grid md:grid-cols-3 grid-cols-2 gap-4 overflow-hidden">
        {items.map((item) => (
          <div
            key={item.id}
            className="cursor-pointer group w-full"
            onClick={() => {
              if (!isUserAuthenticated) {
                setActiveTab('auth')
              } else {
                onProductSelect(item.id)
              }
            }}
          >
            <div className="relative w-full aspect-square rounded-lg overflow-hidden mb-3">
              <img
                src={item.image || '/placeholder.svg'}
                alt={item.title}
                className="w-full h-full object-contain transition-transform duration-300"
              />
            </div>
            <div className="px-2 py-1 flex flex-col items-center justify-center gap-1">
              <h3 className="text-sm font-bold text-base-foreground line-clamp-2 transition-colors text-center max-w-full">
                {item.title}
              </h3>
              {item.price ? (
                <Price
                  amount={item.price.amount}
                  currencyCode={item.price.currencyCode}
                />
              ) : (
                <div className="h-4"></div> // Empty placeholder for consistent spacing
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
