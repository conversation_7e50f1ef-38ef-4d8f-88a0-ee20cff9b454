import { useEffect, useState } from 'react'
import { IconNavMobile } from './components/IconNavMobile'
import { IconNavDesktop } from './components/IconNavDesktop'
import { useAppContext } from '@/context/AppContext'
import { ICON_MAP, NAV_ITEMS } from './constants'
import type { IconNavProps } from '@/types/navigation'
import type { ConfigMenuProps } from '@/types/artist'

import './index.css'

type Property = {
  Name: string
  Value?: string
}

export const IconNav = ({ activeTab, onTabChange, disabled }: IconNavProps) => {
  const { config, colorsInfo } = useAppContext()
  const [configNavigationMenu, setConfigNavigationMenu] = useState<
    ConfigMenuProps[]
  >([])

  console.log('configNavigationMenu: ', configNavigationMenu)

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        const response = await fetch(config?.menu || '')
        const data = await response.json()
        const menuActions = data?.Actions?.filter((a: any) =>
          a.Action?.startsWith('Navigation:Menu:'),
        )

        const menuItems = await Promise.all(
          menuActions.map(async (action: any) => {
            try {
              const res = await fetch(action.Url)
              const innerData = await res.json()
              const props: Property[] = innerData.Properties || []

              return {
                tab: props.find((p) => p.Name === 'Tab')?.Value || '',
                label:
                  props.find((p) => p.Name === 'Content:Title')?.Value || '',
                Icon:
                  ICON_MAP[props.find((p) => p.Name === 'Icon')?.Value || ''] ||
                  (() => null),
              }
            } catch {
              return null
            }
          }),
        )

        setConfigNavigationMenu(menuItems.filter(Boolean))
      } catch (err) {
        console.warn('Could not load navigation config', err)
      }
    }

    if (config?.menu) {
      fetchMenu()
    }
  }, [config])

  useEffect(() => {
    document.documentElement.style.setProperty(
      '--accent-color',
      colorsInfo?.hoverColor || '#1cc4f3',
    )
  }, [colorsInfo?.hoverColor])

  return (
    <>
      <IconNavDesktop
        activeTab={activeTab}
        onTabChange={onTabChange}
        disabled={disabled}
        configNavigationMenu={(configNavigationMenu.length
          ? configNavigationMenu
          : NAV_ITEMS
        ).filter((item) => item.tab !== 'back')}
        fontColor={colorsInfo?.fontColor}
        titleColor={colorsInfo?.secondaryBackgroundColor}
      />
      <IconNavMobile
        activeTab={activeTab}
        onTabChange={onTabChange}
        disabled={disabled}
        configNavigationMenu={
          configNavigationMenu.length ? configNavigationMenu : NAV_ITEMS
        }
        fontColor={colorsInfo?.fontColor}
        titleColor={colorsInfo?.secondaryBackgroundColor}
      />
    </>
  )
}
