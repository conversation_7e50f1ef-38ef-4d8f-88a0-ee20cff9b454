import { IconNavProps } from '@/types/navigation'
import { useUser } from '@/context/UserContext'
import { useAppContext } from '@/context/AppContext'
import { ConfigMenuProps } from '@/types/artist'
import { AppIcon } from '@/components/Icons/AppIcon'
import { NavButton } from '../NavigationButton'
import { UserIcon } from '@/components/Icons'

export const IconNavDesktop = ({
  activeTab,
  onTabChange,
  disabled,
  configNavigationMenu,
  fontColor,
  titleColor,
}: IconNavProps & {
  configNavigationMenu: ConfigMenuProps[]
  fontColor?: string
  titleColor?: string
}) => {
  const { isUserAuthenticated } = useUser()
  const { isMenuOpen, isMenuHovered, setIsMenuHovered, setActiveTabDetails } =
    useAppContext()

  return (
    <nav
      style={{ backgroundColor: titleColor || undefined }}
      className={`${!isMenuOpen && 'hidden'} bg-sidebar-widget-background md:flex flex-col ${
        isMenuHovered
          ? 'w-60 md:fixed left-0 top-0 h-full '
          : 'w-20 min-w-[56px] max-w-[56px]'
      } pt-6 pb-2 z-[50] md:z-[99999999]`}
    >
      {!fontColor && (
        <button className="mb-4 bg-transparent border-none p-0 cursor-default">
          <div className="w-full h-full object-cover flex justify-center items-center">
            <AppIcon />
          </div>
        </button>
      )}
      <div className="flex flex-col gap-2 flex-1 md:justify-between h-full">
        <div
          onMouseEnter={() => setIsMenuHovered(true)}
          onMouseLeave={() => setIsMenuHovered(false)}
        >
          {configNavigationMenu.map(({ tab, Icon, label }) => (
            <NavButton
              key={tab}
              onClick={() => {
                onTabChange(tab)
                setActiveTabDetails(
                  configNavigationMenu.find((item) => item.tab === tab) || null,
                )
              }}
              isActive={activeTab === tab}
              disabled={disabled}
              className="w-full flex justify-start items-left gap-2"
              style={
                {
                  '--font-color': fontColor || '#666c6c',
                } as React.CSSProperties
              }
            >
              <Icon />
              <h4
                className={`block text-center ${
                  isMenuHovered
                    ? 'block font-base text-base'
                    : 'md:hidden font-semibold'
                }`}
              >
                {label}
              </h4>
            </NavButton>
          ))}
        </div>

        <NavButton
          isActive={activeTab === 'profile'}
          onClick={() => onTabChange('profile')}
          disabled={disabled}
          className="w-full flex justify-start items-left gap-2 m-0"
          onMouseEnter={() => setIsMenuHovered(true)}
          onMouseLeave={() => setIsMenuHovered(false)}
          style={
            {
              '--font-color': fontColor || '#666c6c',
            } as React.CSSProperties
          }
        >
          <UserIcon />
          <h4
            className={`block text-center ${
              isMenuHovered
                ? 'block font-base text-base'
                : 'md:hidden font-semibold'
            }`}
          >
            {isUserAuthenticated ? 'My Account' : 'Member Access'}
          </h4>
        </NavButton>
      </div>
    </nav>
  )
}
