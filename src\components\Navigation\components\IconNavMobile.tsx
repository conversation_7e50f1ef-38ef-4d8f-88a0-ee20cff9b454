import { IconNavProps } from '@/types/navigation'
import { useUser } from '@/context/UserContext'
import { useAppContext } from '@/context/AppContext'
import { ConfigMenuProps } from '@/types/artist'
import { AppIcon } from '@/components/Icons/AppIcon'
import { NavButton } from '../NavigationButton'
import { UserIcon } from '@/components/Icons'

export const IconNavMobile = ({
  activeTab,
  onTabChange,
  disabled,
  configNavigationMenu,
  fontColor,
  titleColor,
}: IconNavProps & {
  configNavigationMenu: ConfigMenuProps[]
  fontColor?: string
  titleColor?: string
}) => {
  const { isUserAuthenticated } = useUser()
  const { isMenuOpen, setActiveTabDetails } = useAppContext()

  if (!isMenuOpen) return null

  return (
    <nav className="md:hidden">
      <div
        style={{ backgroundColor: titleColor || undefined }}
        className="fixed inset-0 bg-secondary py-10 z-50 text-white"
      >
        {!fontColor && (
          <div className="md:hidden flex flex-col items-center gap-6 z-60">
            <AppIcon />
          </div>
        )}
        <div className="flex flex-col items-left gap-6 w-full h-full py-10">
          {configNavigationMenu.map(({ tab, Icon, label }) => (
            <NavButton
              key={tab}
              onClick={() => {
                onTabChange(tab)
                setActiveTabDetails(
                  configNavigationMenu.find((item) => item.tab === tab) || null,
                )
              }}
              isActive={activeTab === tab}
              disabled={disabled}
              className="w-full flex justify-start items-left gap-4 pl-4 py-1"
              style={
                {
                  '--font-color': fontColor || '#666c6c',
                } as React.CSSProperties
              }
            >
              <Icon />
              <h4 className="text-xl block">{label}</h4>
            </NavButton>
          ))}
          <NavButton
            isActive={activeTab === 'profile'}
            onClick={() => {
              onTabChange('profile')
              setActiveTabDetails(
                configNavigationMenu.find((item) => item.tab === 'profile') ||
                  null,
              )
            }}
            disabled={disabled}
            className="flex gap-4 pl-4"
            style={
              {
                '--font-color': fontColor || '#666c6c',
              } as React.CSSProperties
            }
          >
            <UserIcon />
            <h4 className="text-xl block">
              {isUserAuthenticated ? 'My Account' : 'Member Access'}
            </h4>
          </NavButton>
        </div>
      </div>
    </nav>
  )
}
