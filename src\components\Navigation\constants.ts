import type { Tab } from '../../types/navigation'
import {
  ShoppingBagIcon,
  ScreenIcon,
  TrophyIcon,
  HouseIcon,
  CircleIcon,
  PhotoIcon,
} from '../../components/Icons'
import { ChatIcon } from '../Icons/ChatIcon'
import { CloseIcon } from '../Icons/CloseIcon'

interface NavItem {
  tab: Tab
  Icon: React.FC<React.SVGProps<SVGSVGElement>>
  label: string
}

export const NAV_ITEMS: NavItem[] = [
  { tab: 'home', Icon: HouseIcon, label: 'Home' },
  { tab: 'chat', Icon: ChatIcon, label: 'Community Chat' },
  { tab: 'virtual-events', Icon: CircleIcon, label: 'Virtual Events' },
  { tab: 'exclusive', Icon: ScreenIcon, label: 'Exclusive Content' },
  { tab: 'shopping', Icon: ShoppingBagIcon, label: 'Merch' },
  { tab: 'rewards', Icon: TrophyIcon, label: 'Loyalty & Rewards' },
  { tab: 'back', Icon: CloseIcon, label: `Back to artist site` },
]

export const ICON_MAP: Record<
  string,
  React.FC<React.SVGProps<SVGSVGElement>>
> = {
  home: HouseIcon,
  chat: ChatIcon,
  events: CircleIcon,
  exclusive: ScreenIcon,
  shopping: ShoppingBagIcon,
  rewards: TrophyIcon,
  back: CloseIcon,
  photo: PhotoIcon,
}
