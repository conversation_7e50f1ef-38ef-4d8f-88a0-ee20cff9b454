.nav-button {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 12.5px;
  /* color: #666c6c; */
  color: var(--font-color, #666c6c);
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  align-items: center;
  min-height: 44px;
}

.nav-button:hover {
  color: var(--accent-color);
}

.nav-button.active {
  color: var(--accent-color);
  position: relative;
  z-index: 1;
}

.nav-button.active::after {
  background: linear-gradient(
    90deg,
    rgba(0, 228, 203, 0.2) 3.7%,
    rgba(0, 228, 203, 0) 100%
  );
}

.nav-button.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--accent-color);
  z-index: 2;
}

.nav-button.logout {
  margin-top: auto;
}
