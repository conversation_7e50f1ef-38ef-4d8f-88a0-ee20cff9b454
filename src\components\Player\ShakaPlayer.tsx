import { useEffect, useRef, useState } from 'react'
import shaka from 'shaka-player/dist/shaka-player.compiled'
import { Slider } from '@/components/ui/slider'
import { Button } from '@/components/ui/button'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  <PERSON>otate<PERSON>cw,
  <PERSON><PERSON><PERSON><PERSON>,
  Ski<PERSON><PERSON><PERSON><PERSON>,
  Minimize,
  Maximize,
} from 'lucide-react'
import getBrowserType from '@/helpers/getBrowserType'
import useFullscreen from '@/hooks/useFullscreen'
import { usePlayerControls } from '@/hooks/usePlayerControls'
import {
  useIncentiveTracker,
  IncentiveEventType,
} from '@/context/IncentiveTrackerContext'
import { PlayPlaceholderIcon } from '../Icons/Player/PlayPlaceholderIcon'
import useContentConsumptionContract from '@/hooks/useContentConsumptionContract'
import useContent from '@/hooks/useContent'

shaka.polyfill.PatchedMediaKeysApple.install()
shaka.polyfill.installAll()

// Update the interface to include loyaltyMatchMerchantId
interface PlayerProps {
  src: string
  poster?: string
  contentId?: string
  loyaltyMatchMerchantId?: string // Add this prop
}

const ShakaPlayer = ({
  src,
  poster,
  contentId,
  loyaltyMatchMerchantId, // Add this parameter
}: PlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<shaka.Player | null>(null)

  const { isFullscreen, toggleFullscreen } = useFullscreen(videoRef)
  // Initialize with isPlaying = false to prevent autoplay
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [showControls, setShowControls] = useState(false)
  const [playerReady, setPlayerReady] = useState(false)

  const { content: mainContent } = useContent(contentId)

  const {
    onPause: pause,
    onPlay: play,
    onEnded: ended,
    onSeek: onSeek,
  } = useContentConsumptionContract(
    mainContent,
    playerRef.current,
    mainContent?.['Content:PlaybackSessionId'],
  )

  const { volume, setVolume } = usePlayerControls(
    videoRef as React.RefObject<HTMLVideoElement>,
  )

  const configureDrm = async () => {
    if (getBrowserType() === 'Safari') {
      return true
    } else {
      return drmToChrome()
    }
  }

  const setChromeHlsManifestParser = () => {
    shaka.media.ManifestParser.registerParserByMime(
      'm3u8',
      () => new shaka.hls.HlsParser(),
    )
    shaka.media.ManifestParser.registerParserByMime(
      'Application/vnd.apple.mpegurl',
      () => new shaka.hls.HlsParser(),
    )
    shaka.media.ManifestParser.registerParserByMime(
      'application/x-mpegURL',
      () => new shaka.hls.HlsParser(),
    )
  }

  const drmToChrome = async () => {
    try {
      setChromeHlsManifestParser()
      playerRef.current?.configure({
        abr: { defaultBandwidthEstimate: 300000 },
        drm: {
          servers: {
            'com.widevine.alpha':
              'https://lic.drmtoday.com/license-proxy-widevine/cenc/',
            'com.microsoft.playready':
              'https://lic.drmtoday.com/license-proxy-widevine/cenc/',
          },
        },
      })

      return true
    } catch (error) {
      console.error('Error configuring DRM for Chrome:', error)
    }
  }

  const initPlayer = async () => {
    if (shaka.Player.isBrowserSupported()) {
      playerRef.current = new shaka.Player(videoRef.current)

      try {
        await configureDrm()

        if (playerRef.current) {
          try {
            // Load the video but don't autoplay
            await playerRef.current.load(src)

            // Don't attempt to play - wait for user interaction
            setIsPlaying(false)
            setPlayerReady(true)
          } catch (error) {
            console.error('Error loading video:', error)
          }
        }
      } catch (error) {
        if (error instanceof shaka.util.Error) {
          console.error('Error code', error.code, 'object', error)
        } else {
          console.error('Unknown error', error)
        }
      }
    } else {
      console.error('Browser not supported!')
    }
  }

  const onPlayClick = async () => {
    if (!playerReady) {
      await initPlayer()
      togglePlay()
    } else {
      togglePlay()
    }
  }

  useEffect(() => {
    if (playerRef.current) {
      playerRef.current.destroy()
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy()
        setPlayerReady(false)
        setIsPlaying(false)
      }
    }
  }, [src])

  // Add this ref to track if incentive was already awarded for this video
  const playTrackedRef = useRef<boolean>(false)

  // Add incentive tracker hook with safe fallback - fix type annotations
  let trackEvent:
    | ((
        eventType: IncentiveEventType,
        metadata?: Record<string, any>, // Add this metadata parameter
      ) => Promise<void>)
    | undefined

  try {
    const { trackEvent: trackerFn } = useIncentiveTracker()
    trackEvent = trackerFn
  } catch (error) {
    console.log('IncentiveTracker not available in this context')
  }

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => setCurrentTime(video.currentTime)
    const handleDurationChange = () => setDuration(video.duration)
    const handlePlay = () => {
      setIsPlaying(true)
      setShowControls(true)
      play()

      // Track video played event with merchantId
      if (contentId && !playTrackedRef.current && trackEvent) {
        trackEvent(IncentiveEventType.VIDEO_PLAYED, {
          merchantId: loyaltyMatchMerchantId, // Add merchantId here
        }).catch((err: Error) =>
          console.error('Failed to track video play:', err),
        )

        // Mark as tracked so we don't award multiple times
        playTrackedRef.current = true
      }
    }
    const handlePause = () => {
      setIsPlaying(false)
      setShowControls(false)
      pause()
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
    }
  }, [contentId, trackEvent])

  useEffect(() => {
    playTrackedRef.current = false
  }, [src])

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (video.paused) {
      video
        .play()
        .then(() => {
          setIsPlaying(true)

          // Track with merchantId here too
          if (contentId && !playTrackedRef.current && trackEvent) {
            trackEvent(IncentiveEventType.VIDEO_PLAYED, {
              merchantId: loyaltyMatchMerchantId, // Add merchantId here
            }).catch((err: Error) =>
              console.error('Failed to track video play:', err),
            )
            playTrackedRef.current = true
          }
        })
        .catch((error) => {
          console.error('Play failed:', error)
          setIsPlaying(false)
        })
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }

  const handleVolumeChange = (newVolume: number[]) => {
    if (!videoRef.current) return

    const volumeValue = newVolume[0]
    videoRef.current.volume = volumeValue
    setVolume(volumeValue)
    localStorage.setItem('playerVolume', volumeValue.toString())
  }

  const handleSeek = (newTime: number[]) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = newTime[0]
    onSeek()
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const seek = (seconds: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(
      0,
      Math.min(video.duration, video.currentTime + seconds),
    )
    onSeek()
  }

  const restart = () => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = 0
    video.play()
  }

  return (
    <div
      ref={containerRef}
      className="relative w-full h-full aspect-video bg-black rounded-lg overflow-hidden"
    >
      <video
        ref={videoRef}
        poster={poster}
        className="w-full h-full"
        controls={false}
        playsInline
        webkit-playsinline="true"
        onClick={onPlayClick}
        onEnded={() => {
          ended()
        }}
        preload="none"
      />
      {/* Add a play button overlay when video is not playing */}
      {!isPlaying && (
        <div
          onClick={onPlayClick}
          className="absolute inset-0 flex items-center justify-center cursor-pointer z-20"
          aria-label="Play video"
        >
          <div className="w-16 h-16 md:w-24 md:h-24 flex items-center justify-center">
            <PlayPlaceholderIcon className="text-black" />
          </div>
        </div>
      )}

      {showControls && (
        <div className="absolute bottom-0 left-0 right-0 p-2 md:p-4 bg-gradient-to-t from-black to-transparent z-30">
          <Slider
            value={[currentTime]}
            max={duration}
            step={1}
            onValueChange={handleSeek}
            className="mb-4 cursor-pointer"
            thumbClassName="hidden"
            trackClassName="bg-muted-foreground rounded-full"
            rangeClassName="bg-primary rounded-full"
          />
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <PlayerButton onClick={onPlayClick}>
                {isPlaying ? (
                  <Pause className="h-4 w-4 md:h-6 md:w-6" />
                ) : (
                  <Play className="h-4 w-4 md:h-6 md:w-6" />
                )}
              </PlayerButton>
              <PlayerButton onClick={() => seek(-10)}>
                <SkipBack className="h-4 w-4 md:h-6 md:w-6" />
              </PlayerButton>
              <PlayerButton onClick={restart}>
                <RotateCcw className="h-4 w-4 md:h-6 md:w-6" />
              </PlayerButton>
              <PlayerButton onClick={() => seek(10)}>
                <SkipForward className="h-4 w-4 md:h-6 md:w-6" />
              </PlayerButton>
              <PlayerButton
                onClick={() => handleVolumeChange([volume === 0 ? 1 : 0])}
              >
                {volume === 0 ? (
                  <VolumeX className="h-4 w-4 md:h-6 md:w-6" />
                ) : (
                  <Volume2 className="h-4 w-4 md:h-6 md:w-6" />
                )}
              </PlayerButton>
              <Slider
                value={[volume]}
                max={1}
                step={0.1}
                onValueChange={handleVolumeChange}
                className="w-12 md:w-24 h-2 cursor-pointer hidden sm:flex"
                thumbClassName="hidden"
                trackClassName="bg-muted-foreground rounded-full"
                rangeClassName="bg-primary rounded-full"
              />
            </div>

            <div className="flex items-center space-x-2">
              <div className="text-sm h-full">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
              <PlayerButton
                onClick={toggleFullscreen}
                className={isFullscreen ? 'text-[#66B8FF]' : ''}
              >
                {isFullscreen ? <Minimize /> : <Maximize />}
              </PlayerButton>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export const PlayerButton: React.FC<
  React.ButtonHTMLAttributes<HTMLButtonElement>
> = ({ children, className, ...props }) => (
  <Button
    size="icon"
    variant="ghost"
    className={`text-white hover:text-[#66B8FF] hover:bg-transparent transition-colors p-1 md:p-2 ${className}`}
    {...props}
  >
    {children}
  </Button>
)

export default ShakaPlayer
