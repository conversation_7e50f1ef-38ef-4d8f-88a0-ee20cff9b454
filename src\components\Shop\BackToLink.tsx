interface BackToLinkProps {
  onBack: () => void
  title?: string
  // Remove the onCartClick prop since it's not being used
}

export default function BackToLink({ onBack, title }: BackToLinkProps) {
  return (
    <div className="flex justify-between">
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-[20px] text-neutral-50 hover:text-neutral-50 transition-colors"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m15 18-6-6 6-6" />
        </svg>
        {title || 'Continue Shopping'}
      </button>
    </div>
  )
}
