import { useCart } from '@/context/CartContext'

interface CartButtonProps {
  onClick: () => void
}

export default function CartButton({ onClick }: CartButtonProps) {
  const { items } = useCart()

  // Calculate total items in cart
  const itemCount = items.reduce((total, item) => total + item.quantity, 0)

  return (
    <div
      onClick={onClick}
      className="text-neutral-50 hover:text-primary/90 transition-colors relative flex justify-end z-50"
    >
      <span className="text-primary text-sm cursor-pointer relative">
        Cart
        {itemCount > 0 && (
          <span className="absolute top-[-15px] right-[-15px] z-50 bg-primary text-zinc-900 text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
            {itemCount}
          </span>
        )}
      </span>
    </div>
  )
}
