interface PriceProps {
  amount: string
  currencyCode: string
}

export default function Price({ amount, currencyCode }: PriceProps) {
  const formatPrice = (amount: string, currencyCode: string) => {
    // Get the base currency symbol ($ for USD, CAD, etc.)
    const baseCurrencySymbol = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    })
      .format(0)
      .charAt(0)

    // Format the number part without currency code
    const formattedNumber = new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(parseFloat(amount))

    // Get country code abbreviation (assuming currencyCode is in ISO format like USD, CAD, etc.)
    const countryCode = currencyCode.substring(0, 2)

    // Return formatted string with country code in parentheses
    return `${baseCurrencySymbol}${formattedNumber} (${countryCode})`
  }

  return (
    <p className="text-neutral-50 text-sm text-center text-medium">
      {formatPrice(amount, currencyCode)}
    </p>
  )
}
