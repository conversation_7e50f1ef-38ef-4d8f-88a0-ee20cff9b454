import { Check, ChevronDown } from 'lucide-react'
import { useEffect, useState } from 'react'
import { shopifyClient } from '@/lib/shopify'
import { GET_HOME_COLLECTION } from '@/queries/shopify-queries'
import { HomeCollectionProduct } from '@/types/merch'

interface SortByTypeListProps {
  onSortChange: (method: string) => void
}

export default function SortByTypeList({ onSortChange }: SortByTypeListProps) {
  const [productTypes, setProductTypes] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [open, setOpen] = useState(false)
  const [selectedType, setSelectedType] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProductTypes() {
      setLoading(true)
      try {
        // Instead of using GET_PRODUCT_TYPES, use GET_HOME_COLLECTION
        // to get actual products and then extract types that are in use
        const response = await shopifyClient.request(GET_HOME_COLLECTION)

        if (response?.data?.collection?.products?.edges) {
          const edges = response.data.collection.products.edges

          // Filter valid products
          const validProducts = edges.filter(
            (edge: HomeCollectionProduct) =>
              edge?.node?.images?.edges[0]?.node?.url &&
              edge?.node?.title &&
              edge?.node?.handle,
          )

          // Extract product types from actual products
          const typesSet = new Set<string>()

          validProducts.forEach((edge: HomeCollectionProduct) => {
            const productType = edge.node.productType
            if (
              productType &&
              productType.trim() !== '' &&
              productType !== 'Unknown'
            ) {
              typesSet.add(productType)
            }
          })

          // Convert to array and sort alphabetically
          const types = Array.from(typesSet).sort((a, b) => a.localeCompare(b))
          setProductTypes(types)
        } else {
          setProductTypes([])
        }
      } catch (error) {
        console.error('Error fetching product types:', error)
        setProductTypes([])
      } finally {
        setLoading(false)
      }
    }

    fetchProductTypes()
  }, [])

  // Handle sort/filter change
  const handleSortChange = (method: string) => {
    onSortChange(method)
    setOpen(false)
  }

  // Clear filters
  const clearFilter = () => {
    onSortChange('default')
    setOpen(false)
  }

  if (loading)
    return (
      <div className="text-muted-foreground text-sm">Loading filters...</div>
    )
  if (productTypes.length === 0) return null

  return (
    <div className="inline-flex flex-col justify-start items-center w-60 relative">
      <button
        onClick={() => setOpen((prev) => !prev)}
        className="relative flex h-10 w-full items-center justify-between rounded-md bg-black text-primary px-3 py-2 text-sm ring-offset-background focus:outline-none"
      >
        <span className="line-clamp-1">{selectedType || 'All Merch'}</span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </button>

      {open && (
        <div className="absolute z-[9999999] mt-8 w-full max-h-96 overflow-hidden rounded-md border bg-popover text-white shadow-md border-1 border-white">
          <div className="flex flex-col p-1 text-left align-left justify-start">
            <button
              className="relative cursor-pointer flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
              onClick={() => {
                clearFilter()
                setSelectedType(null)
              }}
            >
              {!selectedType && (
                <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                  <Check className="h-4 w-4 text-blue-500" />
                </span>
              )}
              All Merch
            </button>
            {productTypes.map((type) => (
              <button
                key={type}
                onClick={() => {
                  handleSortChange(`type-${type}`)
                  setSelectedType(type)
                }}
                className="relative cursor-pointer flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
              >
                {type === selectedType && (
                  <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                    <Check className="h-4 w-4 text-blue-500" />
                  </span>
                )}
                {type}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
