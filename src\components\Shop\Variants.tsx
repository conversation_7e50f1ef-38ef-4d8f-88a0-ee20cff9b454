import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
interface VariantOption {
  name: string
  value: string
}

interface VariantNode {
  id: string
  title: string
  selectedOptions: VariantOption[]
  availableForSale: boolean
}

interface VariantsProps {
  variants: {
    edges: Array<{
      node: VariantNode
    }>
  }
  options?: {
    name: string
    values: string[]
  }[]
  selectedVariant: string | null
  onVariantSelect: (
    matchingVariant: string,
    selectedSize: Record<string, string>,
  ) => void
  onVariantOptionsSelect: (selectedSize: Record<string, string>) => void
}

export default function Variants({
  variants,
  options,
  selectedVariant,
  onVariantSelect,
  onVariantOptionsSelect,
}: VariantsProps) {
  const [selectedOptions, setSelectedOptions] = useState<
    Record<string, string>
  >({})

  // If no options are provided, extract them from variants
  const derivedOptions = options || extractOptionsFromVariants(variants)

  useEffect(() => {
    // Initialize with first available value for each option
    if (derivedOptions) {
      const initialOptions: Record<string, string> = {}
      derivedOptions.forEach((option) => {
        if (option.values.length > 0) {
          initialOptions[option.name] = option.values[0]
        }
      })
      setSelectedOptions(initialOptions)
      onVariantOptionsSelect(initialOptions)

      // Find variant matching these options
      const matchingVariant = findMatchingVariant(
        variants.edges,
        initialOptions,
      )
      if (matchingVariant && matchingVariant !== selectedVariant) {
        onVariantSelect(matchingVariant, selectedOptions)
      }
    }
  }, [])

  if (variants.edges.length <= 1) return null

  // Helper to find a variant ID that matches the selected options
  function findMatchingVariant(
    variantEdges: Array<{ node: VariantNode }>,
    options: Record<string, string>,
  ): string | null {
    // For simple case with just one option (backward compatibility)
    if (Object.keys(options).length === 1) {
      const optionName = Object.keys(options)[0]
      const optionValue = options[optionName]

      const match = variantEdges.find(
        ({ node }) =>
          node.title === optionValue ||
          (node.selectedOptions &&
            node.selectedOptions.some((opt) => opt.value === optionValue)),
      )

      return match ? match.node.id : null
    }

    // For multiple options case
    const match = variantEdges.find(({ node }) => {
      return (
        node.selectedOptions &&
        node.selectedOptions.every((opt) => options[opt.name] === opt.value)
      )
    })

    return match ? match.node.id : null
  }

  // When an option is selected
  const handleOptionChange = (optionName: string, value: string) => {
    const newOptions = {
      ...selectedOptions,
      [optionName]: value,
    }

    setSelectedOptions(newOptions)

    // Find variant matching these options
    const matchingVariant = findMatchingVariant(variants.edges, newOptions)
    if (matchingVariant) {
      onVariantSelect(matchingVariant, newOptions)
    }
  }

  // Size order for sorting
  const sizeOrder: Record<string, number> = {
    XS: 0,
    S: 1,
    M: 2,
    L: 3,
    XL: 4,
    '2XL': 5,
    '3XL': 6,
    '4XL': 7,
  }

  // Define UI for each option type
  return (
    <div className="space-y-3 pt-0">
      <div className="flex items-center justify-between align-center text-[12px] text-muted-foreground w-full h-full">
        <button
          className="h-10 bg-white/0 rounded-md justify-center items-center gap-2 flex"
          type="button"
        >
          <Button
            className="text-muted-foreground text-[12px] leading-tight hover:no-underline p-0"
            variant="link"
          >
            Select Size
          </Button>
        </button>
      </div>
      {derivedOptions
        .sort((a) => (a.name === 'Size' ? -1 : 1))
        .map((option) => (
          <div key={option.name} className="space-y-2">
            {option.name.toLowerCase().includes('color') ? (
              // Color variant UI section
              <>
                <div className="self-stretch w-full py-2 inline-flex justify-center items-center gap-2.5 overflow-hidden">
                  {option.values.map((color) => {
                    const isSelected = selectedOptions[option.name] === color

                    return (
                      <button
                        key={color}
                        onClick={() => handleOptionChange(option.name, color)}
                        className={`w-8 h-8 rounded-full flex items-center justify-center 
                      ${isSelected ? 'ring-purple-500' : ''}`}
                        style={{
                          backgroundColor: getColorCode(color),
                          border: !isSelected ? 'none' : '1px solid #22D3EE',
                        }}
                        aria-label={color}
                        title={color}
                      >
                        {/* {isSelected && (
                        <span className="text-xs text-white">✓</span>
                      )} */}
                      </button>
                    )
                  })}
                </div>
              </>
            ) : (
              // Size or other variant UI
              <div className="flex gap-2 justify-center">
                {[...option.values]
                  .sort((a, b) => {
                    // Sort sizes by predefined order
                    if (option.name.toLowerCase().includes('size')) {
                      const sizeA = a.trim()
                      const sizeB = b.trim()

                      if (sizeA in sizeOrder && sizeB in sizeOrder) {
                        return sizeOrder[sizeA] - sizeOrder[sizeB]
                      }
                      if (sizeA in sizeOrder) return -1
                      if (sizeB in sizeOrder) return 1
                    }
                    return a.localeCompare(b)
                  })
                  .map((value) => {
                    const isSelected = selectedOptions[option.name] === value

                    // Check availability
                    const isAvailable = variants.edges.some(
                      ({ node }) =>
                        node.selectedOptions &&
                        node.selectedOptions.some(
                          (opt) =>
                            opt.name === option.name && opt.value === value,
                        ) &&
                        (node.availableForSale === undefined ||
                          node.availableForSale),
                    )

                    return (
                      <Button
                        key={value}
                        onClick={() => handleOptionChange(option.name, value)}
                        disabled={!isAvailable}
                        variant={isSelected ? 'default' : 'outline'}
                        className={`min-w-[40px] h-[30px] px-4 py-2 text-xs rounded-md border justify-center items-center 
                        ${
                          isSelected
                            ? 'bg-primary text-zinc-950 hover:bg-primary border-zinc-800'
                            : 'bg-zinc-950 text-[#0EA5E9] hover:bg-primary hover:text-zinc-950 border-zinc-800'
                        }
                        ${!isAvailable ? 'opacity-50 cursor-not-allowed' : ''}
                      `}
                      >
                        {value}
                      </Button>
                    )
                  })}
              </div>
            )}
          </div>
        ))}
    </div>
  )
}

// Helper to extract options from variants if not provided
function extractOptionsFromVariants(variants: {
  edges: Array<{ node: VariantNode }>
}): { name: string; values: string[] }[] {
  if (!variants.edges.length) return []

  // If variants have selectedOptions use that
  if (variants.edges[0].node.selectedOptions) {
    const optionsMap = new Map<string, Set<string>>()

    variants.edges.forEach(({ node }) => {
      node.selectedOptions.forEach((opt) => {
        if (!optionsMap.has(opt.name)) {
          optionsMap.set(opt.name, new Set())
        }
        optionsMap.get(opt.name)?.add(opt.value)
      })
    })

    return Array.from(optionsMap.entries()).map(([name, valuesSet]) => ({
      name,
      values: Array.from(valuesSet),
    }))
  }

  // Fallback for backward compatibility - just use titles as single option
  return [
    {
      name: 'Size',
      values: variants.edges.map(({ node }) => node.title),
    },
  ]
}

// Helper to map color names to CSS color codes
function getColorCode(colorName: string): string {
  const colorMap: Record<string, string> = {
    Black: '#000000',
    White: '#FFFFFF',
    Red: '#FF0000',
    Green: '#008000',
    Blue: '#0000FF',
    Yellow: '#FFFF00',
    Purple: '#800080',
    Orange: '#FFA500',
    Pink: '#FFC0CB',
    Grey: '#808080',
    Gray: '#808080',
    Brown: '#A52A2A',
    Navy: '#000080',
    Teal: '#008080',
    Maroon: '#800000',
    Olive: '#808000',
  }

  // Check if the color name exists in our map
  const normalizedColor = colorName.split('/')[0].trim()
  for (const [key, value] of Object.entries(colorMap)) {
    if (normalizedColor.toLowerCase().includes(key.toLowerCase())) {
      return value
    }
  }

  return '#CCCCCC' // Default gray for unknown colors
}
