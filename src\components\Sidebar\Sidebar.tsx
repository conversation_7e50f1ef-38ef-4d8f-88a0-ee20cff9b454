import { useState, useCallback, useEffect } from 'react'
import { IconNav } from '../Navigation/Navigation'
import { TabContent } from '../TabContent'
import type { Tab } from '@/types/navigation'
import { useAppContext } from '@/context/AppContext'
import { useUser } from '@/context/UserContext'
import { PanelLeftClose as PanelRightCloseIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarProps {
  toggleSidebar: () => void
  isOpen: boolean
}

const Sidebar = ({ isOpen, toggleSidebar }: SidebarProps) => {
  const { activeTab, setActiveTab, setIsMenuOpen, colorsInfo } = useAppContext()
  const { isUserAuthenticated } = useUser()
  const [isTabChanging, setIsTabChanging] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  const handleTabChange = useCallback((tab: Tab) => {
    setIsTabChanging(true)
    setIsExpanded(
      tab === 'virtual-event-details' || tab === 'exclusive' || tab === 'photo',
    )
    setActiveTab(tab)
    setIsTabChanging(false)
    setIsMenuOpen(false)
  }, [])

  useEffect(() => {
    handleTabChange(activeTab)
  }, [activeTab, handleTabChange])

  useEffect(() => {
    if (
      !isUserAuthenticated &&
      (activeTab === 'chat' ||
        activeTab === 'profile' ||
        activeTab === 'virtual-events' ||
        activeTab === 'exclusive' ||
        activeTab === 'photo')
    ) {
      setActiveTab('auth')
    }
  }, [isUserAuthenticated, activeTab])

  useEffect(() => {
    if (!isOpen) {
      setIsExpanded(false)
      setActiveTab('home')
    }
  }, [isOpen])

  const color = colorsInfo?.secondaryBackgroundColor

  return (
    <div
      className={cn(
        'fixed top-0 flex h-full flex-col backdrop-blur-md transition-all duration-300 ease-in-out w-full md:w-[537px] shadow-[-2px_0_5px_rgba(0,0,0,0.1)]',
        isExpanded && 'md:w-[1000px] sm:w-screen',
        isOpen ? 'right-0' : '-right-[537px]',
      )}
    >
      <div className="flex h-full w-full">
        <IconNav onTabChange={handleTabChange} activeTab={activeTab} />
        <TabContent
          isChanging={isTabChanging}
          activeTab={activeTab}
          toggleSidebar={toggleSidebar}
        />
      </div>

      <button
        style={{ color: color || undefined }}
        className="md:block hidden absolute md:right-1 right-2 md:top-0 top-4 z-40 cursor-pointer border-none bg-none p-4 text-xl md:z-[999999] rotate-180"
        onClick={toggleSidebar}
      >
        <PanelRightCloseIcon />
      </button>
    </div>
  )
}

export default Sidebar
