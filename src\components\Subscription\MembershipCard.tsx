import { MembershipPlan } from '@/types/subscription'

type MembershipCardProps = {
  membershipItem: MembershipPlan
}

const MembershipCard = ({ membershipItem }: MembershipCardProps) => {
  return (
    <div className="w-full p-6 min-h-48 md:min-h-96 bg-background shadow-md text-center">
      <h2 className="font-semibold mb-2">
        $ {membershipItem['Product:Price']}
      </h2>
      <p className="text-sm font-medium mb-2">
        {membershipItem['Product:Name']}
      </p>
      <div className="text-muted-foreground mb-1 text-sm">
        {(membershipItem['Product:Description'] || '')
          .split('\n')
          .map((line, index) => (
            <p key={index} className="mb-1">
              {line}
            </p>
          ))}
      </div>
    </div>
  )
}

export default MembershipCard
