import { ReactElement } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements } from '@stripe/react-stripe-js'
// import CheckoutForm from '../CheckoutForm'
import { MembershipPlan } from '@/types/subscription'
import CheckoutForm from './CheckoutForm'

const stripePromise = loadStripe(
  'pk_test_51RecxOIhAqXGxPPcfJ5gKjWeyxoo4rYeF8AIQVQ9Snmf8t3S7bcNZXMV8JDgqolhwiI7IWtP7rhjPvJXn3ZHIDI000ssnqEzA1',
)

interface Props {
  planInfo: MembershipPlan
  closeCheckout: () => void
  source?: string
}

const StripeForm = ({ planInfo, closeCheckout }: Props): ReactElement => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm planInfo={planInfo} closeCheckout={closeCheckout} />
    </Elements>
  )
}

export default StripeForm
