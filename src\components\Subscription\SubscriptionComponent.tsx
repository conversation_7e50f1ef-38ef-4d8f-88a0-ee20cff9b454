import MembershipsCarousel from './MembershipCarousel'
import { lazy, useState, startTransition } from 'react'
import { MembershipPlan } from '@/types/subscription'
import useMembershipItem from '@/hooks/useMembershipItem'
import { useFullContent } from '@/hooks/useFullContent'
import { useAppContext } from '@/context/AppContext'
const StripeForm = lazy(() => import('./StripeForm'))

export const SubscriptionComponent = ({
  contentId,
  title,
}: {
  title: string | undefined
  contentId: string | undefined
}) => {
  const { content } = useFullContent(contentId)

  const { colorsInfo } = useAppContext()

  const { memberships } = useMembershipItem({
    content: contentId ? content : undefined,
  })

  const [selectedPlan, setSelectedPlan] = useState<MembershipPlan | undefined>(
    undefined,
  )

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="flex flex-col items-center w-full ">
      <h1
        style={{ color: titleColor || undefined }}
        className="md:sticky md:top-6 md:z-[999999] mb-12 md:text-3xl text-2xl text-center font-semibold"
      >
        {title || ''}
      </h1>
      <h1 style={{ color: titleColor || undefined }} className="mb-12 text-2xl">
        Payment Options
      </h1>
      {!!memberships?.length && !selectedPlan && (
        <MembershipsCarousel
          handlePlanSelect={(plan) =>
            startTransition(() => {
              setSelectedPlan(plan)
            })
          }
          slides={memberships}
        />
      )}

      {selectedPlan && (
        <StripeForm
          closeCheckout={() => setSelectedPlan(undefined)}
          planInfo={selectedPlan}
        />
      )}
    </div>
  )
}

export default SubscriptionComponent
