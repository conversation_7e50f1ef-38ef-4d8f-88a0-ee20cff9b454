import ProfileTab from './Tabs/ProfileTab/ProfileTab'
import { Tab } from '@/types/navigation'
import { useShop } from '@/context/ShopContext'
import { ShopifyErrorBoundary } from '@/components/ErrorBoundaries/ShopifyErrorBoundary'
import { VirtualEventsListPage } from './Tabs/VirtualEvents/VirtualEventsListPage'
import { ExclusiveContent } from './Tabs/ExclusiveContent'
import RewardsTab from './Tabs/RewardTab/RewardTab'
import ShoppingTab from './Tabs/ShoppingTab'
import LiveChat from './Chat/LiveChat'
import HomeTab from './Tabs/HomeTab'
import Login from './Auth/Login'
import { HomeCollectionProduct } from '@/types/merch'
import VirtualEventPlayerPage from './Tabs/VirtualEvents/VirtualEventPlayerPage'
import { GlobalModal } from './GlobalModal'
import { useAppContext } from '@/context/AppContext'
import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'
import { ExclusivePhoto } from './Tabs/ExclusivePhoto'
import { useUser } from '@/context/UserContext'

interface TabContentProps {
  activeTab: Tab
  isChanging: boolean
  toggleSidebar: () => void
}

export const TabContent = ({
  activeTab,
  isChanging,
  toggleSidebar,
}: TabContentProps) => {
  // Use the shop context
  const { loading, error, data } = useShop()
  const { isMenuHovered, artistId, colorsInfo } = useAppContext() // Add loading state
  const { isUserAuthenticated } = useUser()

  const [hasTriggeredBack, setHasTriggeredBack] = useState(false)

  useEffect(() => {
    if (activeTab === 'back' && !hasTriggeredBack) {
      toggleSidebar()
      setHasTriggeredBack(true)
    } else if (activeTab !== 'back' && hasTriggeredBack) {
      setHasTriggeredBack(false)
    }
  }, [activeTab, hasTriggeredBack, toggleSidebar])

  if (activeTab === 'back') return null

  const tabColor = colorsInfo?.backgroundColor

  // This function renders the tab content properly wrapped with error boundary where needed
  const renderContent = () => {
    // Special cases for tabs that don't need shop data
    if (activeTab === 'auth') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <Login />
        </div>
      )
    }

    if (activeTab === 'chat') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 py-6 w-full bg-sidebar-hub-background"
        >
          <LiveChat
            roomId={`comunity-chat-${artistId}`}
            title="Community Chat"
          />
        </div>
      )
    }

    ;<div
      style={{ backgroundColor: tabColor || undefined }}
      className="w-full min-h-full h-fit bg-sidebar-hub-background"
    >
      <Login />
    </div>

    if (activeTab === 'rewards') {
      return !isUserAuthenticated ? (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <Login />
        </div>
      ) : (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          {!isUserAuthenticated ? <Login /> : <RewardsTab />}
        </div>
      )
    }

    if (activeTab === 'profile') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <ProfileTab />
        </div>
      )
    }

    if (activeTab === 'virtual-events') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 py-6 w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <VirtualEventsListPage />
        </div>
      )
    }

    // For tabs that need shop data (home, shopping), prepare the data or handle errors
    if (loading) return <div className="p-6">Loading shop data...</div>

    // For errors, use actual error message inside component to trigger error boundary
    if (error || !data?.collection?.products?.edges) {
      return (
        <div className="p-6">
          {/* This component will throw an error during render to trigger the boundary */}
          <ErrorThrower
            message={error?.message || 'Failed to load merchandise data'}
          />
        </div>
      )
    }

    // Process the data for components that need it
    const slides = data.collection.products.edges
      .filter(
        (edge: HomeCollectionProduct) =>
          edge?.node?.images?.edges[0]?.node?.url &&
          edge?.node?.title &&
          edge?.node?.handle,
      )
      .map((edge: HomeCollectionProduct) => ({
        id: edge.node.handle,
        image: edge.node.images.edges[0].node.url,
        title: edge.node.title,
      }))

    if (activeTab === 'home') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 py-6 w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <HomeTab slides={slides} />
        </div>
      )
    }

    if (activeTab === 'exclusive') {
      return <ExclusiveContent />
    }

    if (activeTab === 'photo') {
      return <ExclusivePhoto />
    }

    if (activeTab === 'virtual-event-details') {
      return <VirtualEventPlayerPage slides={slides} />
    }

    if (activeTab === 'shopping') {
      return (
        <div
          style={{ backgroundColor: tabColor || undefined }}
          className="px-6 py-5 w-full min-h-full h-fit bg-sidebar-hub-background"
        >
          <ShoppingTab />
        </div>
      )
    }

    return null
  }

  return (
    <div
      style={{
        backgroundColor: tabColor || undefined,
        opacity: tabColor ? 0.93 : 1,
      }}
      className={cn(
        'relative flex flex-grow justify-center overflow-y-auto bg-sidebar-widget-background',
        'transition-opacity duration-300 ease-in-out',
        isChanging && 'opacity-50',
        isMenuHovered && 'ml-[56px]',
      )}
    >
      <ShopifyErrorBoundary>
        {activeTab !== 'shopping' && (
          <div
            className="hidden md:block fixed top-0 flex backdrop-blur-md shadow-[-2px_0_5px_rgba(0,0,0,0.1)] z-[9999]"
            style={{
              width: 'calc(100% - 60px)',
              height: '80px',
              backgroundColor: tabColor || 'transparent',
            }}
          />
        )}
        {renderContent()}
        <GlobalModal />
      </ShopifyErrorBoundary>
    </div>
  )
}

const ErrorThrower = ({ message }: { message: string }) => {
  throw new Error(message)
}
