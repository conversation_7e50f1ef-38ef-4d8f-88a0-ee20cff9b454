import { useCart } from '@/context/CartContext'
import { Button } from '@/components/ui/button'
import BackToLink from '@/components/Shop/BackToLink'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'
import { Trash2 } from 'lucide-react'

interface CartProps {
  onBack: () => void
}

export default function Cart({ onBack }: CartProps) {
  const { setIsMenuOpen, colorsInfo } = useAppContext()
  const { items, removeFromCart, checkout, isLoading } = useCart()

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="flex-1 overflow-auto">
      <div className="flex md:gap-4 gap-2 align-center text-center mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <BackToLink onBack={onBack} title="Merch" />
      </div>

      <h1 className="text-3xl font-bold mb-6 text-white">Your Cart</h1>
      <h3 className="text-white text-2xl font-semibold space-y-3">
        Order Summary
      </h3>
      <div className="space-y-4">
        {items.length === 0 ? (
          <div>
            <p className="text-left text-white text-p font-normal mt-6">
              Your shopping cart is empty.
            </p>
            <Button
              size="lg"
              className="w-80 bg-primary hover:bg-primary rounded-md inline-flex justify-center items-center gap-2 mt-8"
              onClick={onBack}
            >
              Continue Shopping
            </Button>
          </div>
        ) : (
          <>
            <div className="space-y-4 mt-6">
              {items.map((item) => (
                <div key={item.variantId} className="flex items-center gap-4">
                  {item.image && (
                    <div className="relative w-16 h-16">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-16 h-16 object-cover rounded-2xl"
                      />
                      <div className="absolute top-[-10px] right-0 w-[21px] h-[21px] rounded-full bg-muted-foreground text-[12px] flex items-center justify-center z-10">
                        <p className="font-medium text-foreground">
                          {item.quantity}
                        </p>
                      </div>
                    </div>
                  )}
                  <div className="flex-1">
                    <p className="font-medium text-foreground">
                      {item.title} - {item.size} ({item.color})
                    </p>
                  </div>

                  <div className="flex gap-2 items-center">
                    {/* <p className="text-sm text-foreground">{item.price}</p> */}

                    <div
                      onClick={() => removeFromCart(item.variantId)}
                      className="text-foreground hover:text-foreground/80 cursor-pointer"
                    >
                      <Trash2 />
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 flex flex-col space-y-4">
              <div className="w-full flex justify-between items-center">
                <p>Subtotal</p>
                <p className="text-muted-foreground text-sm">
                  $
                  {items
                    .reduce(
                      (total: any, item: any) =>
                        total +
                        parseFloat(item.price.replace(/[^0-9.]/g, '')) *
                          item.quantity,
                      0,
                    )
                    .toFixed(2)}
                </p>
              </div>
              <div className="w-full flex justify-between items-center">
                <p>Shipping</p>
                <p className="text-muted-foreground text-sm">
                  Calculated at Checkout
                </p>
              </div>
            </div>
            <div className="mt-8 pt-4">
              <Button
                className="w-full bg-primary hover:bg-primary disabled:text-primary-foreground"
                onClick={checkout}
                disabled={isLoading}
              >
                {isLoading ? 'Redirecting to Checkout...' : 'Go to Checkout'}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
