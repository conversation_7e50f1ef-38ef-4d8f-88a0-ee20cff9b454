import { getCustomerById } from '@/api/user'
import { useEffect, useState } from 'react'
import { ContentDetails } from '@/types/content'
import { fetchAndParseContentByUrl } from '@/api/app'
import useContentAuthorize from '@/hooks/useContentAuthorize'
import ShakaPlayer from '../Player/ShakaPlayer'
import { Skeleton } from '../ui/skeleton'
import { useAppContext } from '@/context/AppContext'
import { useUser } from '@/context/UserContext'
import AuthPlayerOverlay from '../Player/AuthPlayerOverlay'
import SubscriptionComponent from '../Subscription/SubscriptionComponent'
import { useModal } from '@/context/ModalContext'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { PlayPlaceholderIcon } from '../Icons/Player/PlayPlaceholderIcon'
import useContentHitContract from '@/hooks/useContentHitContract'
import { TokenRefresher } from '../TokenRefresher'
import useWebsocket from '@/hooks/useWebsocket'

export const ExclusiveContent = () => {
  const {
    setActiveTab,
    setIsMenuOpen,
    artistId,
    isContentPaymentUpdated,
    setIsContentPaymentUpdated,
    colorsInfo,
    activeTabDetails,
    loyaltyMatchMerchantId,
  } = useAppContext()
  const { isUserAuthenticated } = useUser()
  const { openModal } = useModal()

  const [selectedContent, setSelectedContent] = useState<ContentDetails | null>(
    null,
  )

  const [exclusiveContentList, setExclusiveContent] = useState<
    ContentDetails[] | undefined
  >(undefined)

  const {
    data: authorizedContentData,
    isLoading: authContentLoading,
    error,
    errorCode,
    refetch,
  } = useContentAuthorize(
    artistId && selectedContent ? [selectedContent as ContentDetails] : [],
  )

  const [isECLoading, setECIsLoading] = useState(true)

  useWebsocket()

  useContentHitContract(selectedContent || undefined)

  // Handle authorization errors
  useEffect(() => {
    if (errorCode === '401') {
      console.log('Authorization error detected, refreshing content')
      refetch()
    }
  }, [errorCode, refetch])

  useEffect(() => {
    if (isContentPaymentUpdated) {
      refetch()
      setIsContentPaymentUpdated(false)
    }
  }, [isContentPaymentUpdated, refetch])

  useEffect(() => {
    const fetchArtist = async () => {
      setECIsLoading(true)
      try {
        const artistDetails = await getCustomerById(artistId ?? null)
        if (artistDetails) {
          await fetchArtistExclusiveContent(
            artistDetails as unknown as ContentDetails,
          )
        }
      } catch (error) {
        console.error('Error fetching artist details:', error)
      } finally {
        setECIsLoading(false)
      }
    }

    if (artistId) {
      fetchArtist()
    }
  }, [artistId])

  const fetchArtistExclusiveContent = async (currentArtist: ContentDetails) => {
    try {
      if (!currentArtist?.['Customer:Service:ExclusiveUrl']) return

      const exclusiveContent = await fetchAndParseContentByUrl(
        currentArtist?.['Customer:Service:ExclusiveUrl'] ?? '',
      )

      if (exclusiveContent?.data?.length) {
        setExclusiveContent(exclusiveContent.data)
        setSelectedContent(exclusiveContent.data[0])
      }
    } catch (error) {
      console.error('Error fetching exclusive content:', error)
    }
  }

  const handleVideoChange = (selectedContent: ContentDetails) => {
    setSelectedContent(selectedContent)
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor

  return (
    <div className="w-full h-full md:z-[999999]">
      <TokenRefresher />
      {authContentLoading || isECLoading ? (
        <div className="flex flex-col md:flex-row h-full">
          <div className="w-full md:w-[70%] lg:w-[60%] px-6 py-6">
            <div className="md:sticky top-6 flex md:gap-4 gap-2 align-left text-left mb-6">
              <button
                style={{ color: titleColor || '#22D3EE' }}
                className="flex flex-col justify-center md:hidden"
                onClick={() => setIsMenuOpen(true)}
              >
                <MobileMenuIcon />
              </button>
              <h1
                style={{ color: titleColor || undefined }}
                className="md:text-3xl text-2xl flex flex-col justify-center font-semibold md:z-[999999] relative md:bottom-1.5"
              >
                {activeTabDetails
                  ? activeTabDetails.label
                  : 'Exclusive Content'}
              </h1>
            </div>

            <div className="relative w-full">
              <Skeleton
                style={{ backgroundColor: titleColor || undefined }}
                className="w-full h-10 mb-10"
              />
              <div className="flex flex-col md:flex-row gap-10 h-full">
                <Skeleton
                  style={{ backgroundColor: titleColor || undefined }}
                  className="w-full h-[20rem]"
                />
              </div>
              <Skeleton
                style={{ backgroundColor: titleColor || undefined }}
                className="w-full h-20 my-10"
              />
            </div>
          </div>

          <div
            style={{ backgroundColor: backgroundColor || undefined }}
            className="w-full md:w-[30%] lg:w-[40%] overflow-hidden md:overflow-y-auto bg-sidebar-hub-background p-3 flex flex-col md:z-[999999]"
          >
            <div>
              <h1 className="md:invisible mb-1 hidden md:block">title</h1>
              <h3
                style={{ color: titleColor || undefined }}
                className="mb-3 font-semibold text-2xl"
              >
                Videos
              </h3>
            </div>

            <div className="space-y-6 h-full overflow-auto">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="w-full h-[11rem] bg-sidebar-widget-background"
                  style={{ backgroundColor: titleColor || undefined }}
                />
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row h-full">
          <div className="w-full h-full flex flex-col md:flex-row justify-between">
            <div className="md:overflow-y-auto flex md:flex-row flex-col">
              <div className="w-full md:w-[60%] px-6 py-6">
                <div className="md:sticky top-6 flex md:gap-4 gap-2 align-left text-left mb-6 md:z-[999999]">
                  <button
                    style={{ color: titleColor || '#22D3EE' }}
                    className="flex flex-col justify-center md:hidden"
                    onClick={() => setIsMenuOpen(true)}
                  >
                    <MobileMenuIcon />
                  </button>
                  <h1
                    style={{ color: titleColor || undefined }}
                    className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
                  >
                    {activeTabDetails
                      ? activeTabDetails.label
                      : 'Exclusive Content'}
                  </h1>
                </div>

                {selectedContent?.['Globalization:en-US:Content:Title'] && (
                  <h3
                    style={{ color: titleColor || undefined }}
                    className="mt-0 mb-3 text-3xl font-semibold"
                  >
                    {selectedContent?.['Globalization:en-US:Content:Title']}
                  </h3>
                )}

                <div className="w-full h-auto">
                  {error === 'ERROR_UNAUTHORIZED' ? (
                    <div className="relative w-full h-full aspect-video">
                      <AuthPlayerOverlay
                        handleClick={() =>
                          openModal(
                            <SubscriptionComponent
                              title={
                                selectedContent?.[
                                  'Globalization:en-US:Content:Title'
                                ]
                              }
                              contentId={selectedContent?.['Content:Id']}
                            />,
                          )
                        }
                        poster={
                          selectedContent?.['Customer:Image:LandscapeUrl'] ||
                          selectedContent?.['Content:Thumbnail:Url']
                        }
                      />
                    </div>
                  ) : (
                    <div className="relative w-full h-full aspect-video">
                      <ShakaPlayer
                        src={
                          authorizedContentData?.length
                            ? authorizedContentData[0]['Content:Url']
                            : exclusiveContentList?.[0]['Content:Url'] || ''
                        }
                        poster={
                          selectedContent?.['Content:Landscape:Url'] ||
                          selectedContent?.['Content:Thumbnail:Url']
                        }
                        contentId={selectedContent?.['Content:Id']}
                        loyaltyMatchMerchantId={loyaltyMatchMerchantId}
                      />
                    </div>
                  )}
                </div>
                {selectedContent?.[
                  'Globalization:en-US:Content:Description'
                ] && (
                  <div className="mt-4 space-y-2">
                    <p
                      style={{ color: titleColor || undefined }}
                      className="text-sm"
                    >
                      <span
                        dangerouslySetInnerHTML={{
                          __html:
                            selectedContent?.[
                              'Globalization:en-US:Content:Description'
                            ] || '',
                        }}
                      />
                    </p>
                  </div>
                )}
              </div>
              <div
                style={{
                  backgroundColor: backgroundColor || undefined,
                  opacity: 1,
                  zIndex: 999999,
                }}
                className="w-full md:w-[40%] overflow-hidden md:overflow-y-auto bg-sidebar-hub-background p-3 flex flex-col md:z-[999999]"
              >
                <div>
                  <h1 className="md:invisible mb-1 hidden md:block">title</h1>
                  <h3
                    style={{ color: titleColor || undefined }}
                    className="mb-3 font-semibold text-2xl"
                  >
                    Videos
                  </h3>
                </div>

                <div className="h-full overflow-auto">
                  {exclusiveContentList
                    ?.filter(
                      (event) =>
                        event['Content:Id'] !== selectedContent?.['Content:Id'],
                    )
                    .map((event) => (
                      <div
                        onClick={() => {
                          if (!isUserAuthenticated) {
                            setActiveTab('auth')
                          } else {
                            handleVideoChange(event)
                          }
                        }}
                        className="cursor-pointer w-full px-[2.5px] py-2.5"
                        key={event['Content:Id']}
                      >
                        <div className="aspect-video w-full relative">
                          <img
                            src={
                              event['Content:Landscape:Url'] ||
                              event['Content:Thumbnail:Url']
                            }
                            className="w-full h-full object-contain rounded-lg mx-auto"
                            alt={event['Content:Title']}
                          />
                          <div
                            className="absolute inset-0 flex items-center justify-center z-20"
                            aria-label="Play video"
                          >
                            <div className="w-16 h-16 md:w-24 md:h-24 flex items-center justify-center">
                              <PlayPlaceholderIcon className="text-black" />
                            </div>
                          </div>
                        </div>

                        <h3
                          style={{ color: titleColor || undefined }}
                          className="mt-3 font-semibold text-xl"
                        >
                          {event['Globalization:en-US:Content:Title']}
                        </h3>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
