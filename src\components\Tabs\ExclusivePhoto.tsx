import { getCustomerById } from '@/api/user'
import { useEffect, useState } from 'react'
import { ContentDetails } from '@/types/content'
import { fetchAndParseContentByUrl } from '@/api/app'
import { Skeleton } from '../ui/skeleton'
import { useAppContext } from '@/context/AppContext'
import { useUser } from '@/context/UserContext'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { TokenRefresher } from '../TokenRefresher'

export const ExclusivePhoto = () => {
  const {
    setActiveTab,
    setIsMenuOpen,
    artistId,
    colorsInfo,
    activeTabDetails,
  } = useAppContext()
  const { isUserAuthenticated } = useUser()

  const [selectedContent, setSelectedContent] = useState<ContentDetails | null>(
    null,
  )

  const [exclusiveContentList, setExclusiveContent] = useState<
    ContentDetails[] | undefined
  >(undefined)

  const [isECLoading, setECIsLoading] = useState(true)

  useEffect(() => {
    const fetchArtist = async () => {
      setECIsLoading(true)
      try {
        const artistDetails = await getCustomerById(artistId ?? null)
        if (artistDetails) {
          await fetchArtistExclusivePhotos(
            artistDetails as unknown as ContentDetails,
          )
        }
      } catch (error) {
        console.error('Error fetching artist details:', error)
      } finally {
        setECIsLoading(false)
      }
    }

    if (artistId) {
      fetchArtist()
    }
  }, [artistId])

  const fetchArtistExclusivePhotos = async (currentArtist: ContentDetails) => {
    try {
      if (!currentArtist?.['Customer:Service:ExclusiveUrlPhotos']) return

      const exclusiveContent = await fetchAndParseContentByUrl(
        currentArtist?.['Customer:Service:ExclusiveUrlPhotos'] ?? '',
      )

      if (exclusiveContent?.data?.length) {
        setExclusiveContent(exclusiveContent.data)
        setSelectedContent(exclusiveContent.data[0])
      }
    } catch (error) {
      console.error('Error fetching exclusive content:', error)
    }
  }

  const handleVideoChange = (selectedContent: ContentDetails) => {
    setSelectedContent(selectedContent)
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor

  return (
    <div className="w-full h-full md:z-[999999]">
      <TokenRefresher />
      {isECLoading ? (
        <div className="flex flex-col md:flex-row h-full">
          <div className="w-full md:w-[70%] lg:w-[60%] px-6 py-6">
            <div className="md:sticky top-6 flex md:gap-4 gap-2 align-left text-left mb-6">
              <button
                style={{ color: titleColor || '#22D3EE' }}
                className="flex flex-col justify-center md:hidden"
                onClick={() => setIsMenuOpen(true)}
              >
                <MobileMenuIcon />
              </button>
              <h1
                style={{ color: titleColor || undefined }}
                className="md:text-3xl text-2xl flex flex-col justify-center font-semibold md:z-[999999]"
              >
                {activeTabDetails ? activeTabDetails.label : 'Exclusive Photos'}
              </h1>
            </div>

            <div className="relative w-full">
              <Skeleton
                style={{ backgroundColor: titleColor || undefined }}
                className="w-full h-10 mb-10"
              />
              <div className="flex flex-col md:flex-row gap-10 h-full">
                <Skeleton
                  style={{ backgroundColor: titleColor || undefined }}
                  className="w-full h-[20rem]"
                />
              </div>
              <Skeleton
                style={{ backgroundColor: titleColor || undefined }}
                className="w-full h-20 my-10"
              />
            </div>
          </div>

          <div
            style={{ backgroundColor: backgroundColor || undefined }}
            className="w-full md:w-[30%] lg:w-[40%] overflow-hidden md:overflow-y-auto bg-sidebar-hub-background p-3 flex flex-col md:z-[999999]"
          >
            <div>
              <h1 className="md:invisible mb-1 hidden md:block">title</h1>
              <h3
                style={{ color: titleColor || undefined }}
                className="mb-3 font-semibold text-2xl"
              >
                Galleries
              </h3>
            </div>

            <div className="space-y-6 h-full overflow-auto">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="w-full h-[11rem] bg-sidebar-widget-background"
                  style={{ backgroundColor: titleColor || undefined }}
                />
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row h-full">
          <div className="w-full h-full flex flex-col md:flex-row justify-between">
            <div className="md:overflow-y-auto flex md:flex-row flex-col">
              <div className="w-full md:w-[60%] lg:w-[60%] px-6 py-6">
                <div className="md:sticky top-6 flex md:gap-4 gap-2 align-left text-left mb-6 md:z-[999999]">
                  <button
                    style={{ color: titleColor || '#22D3EE' }}
                    className="flex flex-col justify-center md:hidden"
                    onClick={() => setIsMenuOpen(true)}
                  >
                    <MobileMenuIcon />
                  </button>
                  <h1
                    style={{ color: titleColor || undefined }}
                    className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
                  >
                    {activeTabDetails
                      ? activeTabDetails.label
                      : 'Exclusive Photos'}
                  </h1>
                </div>

                {selectedContent?.['Globalization:en-US:Content:Title'] && (
                  <h3
                    style={{ color: titleColor || undefined }}
                    className="mt-0 mb-3 text-3xl font-semibold"
                  >
                    {selectedContent?.['Globalization:en-US:Content:Title']}
                  </h3>
                )}

                <div className="w-full h-auto">
                  <img
                    src={
                      selectedContent?.['Content:Landscape:Url'] ||
                      selectedContent?.['Content:Thumbnail:Url']
                    }
                    className="w-full h-full object-contain rounded-lg mx-auto"
                    alt={selectedContent?.['Content:Title']}
                  />
                </div>
                {selectedContent?.[
                  'Globalization:en-US:Content:Description'
                ] && (
                  <div className="mt-4 space-y-2">
                    <p
                      style={{ color: titleColor || undefined }}
                      className="text-sm"
                    >
                      <span
                        dangerouslySetInnerHTML={{
                          __html:
                            selectedContent?.[
                              'Globalization:en-US:Content:Description'
                            ] || '',
                        }}
                      />
                    </p>
                  </div>
                )}
              </div>
              <div
                style={{ backgroundColor: backgroundColor || undefined }}
                className="w-full md:w-[40%] overflow-hidden md:overflow-y-auto bg-sidebar-hub-background p-3 flex flex-col md:z-[999999]"
              >
                <div>
                  <h1 className="md:invisible mb-1 hidden md:block">title</h1>
                  <h3
                    style={{ color: titleColor || undefined }}
                    className="mb-3 font-semibold text-2xl"
                  >
                    Galleries
                  </h3>
                </div>

                <div className="h-full overflow-auto">
                  {exclusiveContentList
                    ?.filter(
                      (event) =>
                        event['Content:Id'] !== selectedContent?.['Content:Id'],
                    )
                    .map((event) => (
                      <div
                        onClick={() => {
                          if (!isUserAuthenticated) {
                            setActiveTab('auth')
                          } else {
                            handleVideoChange(event)
                          }
                        }}
                        className="cursor-pointer w-full px-[2.5px] py-2.5"
                        key={event['Content:Id']}
                      >
                        <div className="aspect-video w-full relative">
                          <img
                            src={
                              event['Content:Landscape:Url'] ||
                              event['Content:Thumbnail:Url']
                            }
                            className="w-full h-full object-contain rounded-lg mx-auto"
                            alt={event['Globalization:en-US:Content:Title']}
                          />
                        </div>

                        <h3
                          style={{ color: titleColor || undefined }}
                          className="mt-3 font-semibold text-xl"
                        >
                          {event['Globalization:en-US:Content:Title']}
                        </h3>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
