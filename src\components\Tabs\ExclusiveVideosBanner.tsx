import { fetchAndParseContentByUrl } from '@/api/app'
import { getCustomerById } from '@/api/user'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { useAppContext } from '@/context/AppContext'
import { ContentDetails } from '@/types/content'
import { useEffect, useState } from 'react'
import AuthPlayerOverlay from '../Player/AuthPlayerOverlay'
import ShakaPlayer from '../Player/ShakaPlayer'
import useContentAuthorize from '@/hooks/useContentAuthorize'
import { useUser } from '@/context/UserContext'
import { ICON_MAP } from '../Navigation/constants'

const ExclusiveVideosBanner = () => {
  const { artistId, colorsInfo, setActiveTab, setActiveTabDetails } =
    useAppContext()
  const { isUserAuthenticated } = useUser()

  const [selectedContent, setSelectedContent] = useState<ContentDetails | null>(
    null,
  )

  const { data: authorizedContentData, error } = useContentAuthorize(
    artistId && selectedContent ? [selectedContent as ContentDetails] : [],
  )

  useEffect(() => {
    const fetchArtist = async () => {
      try {
        const artistDetails = await getCustomerById(artistId ?? null)
        if (artistDetails) {
          await fetchArtistExclusiveContent(
            artistDetails as unknown as ContentDetails,
          )
        }
      } catch (error) {
        console.error('Error fetching artist details:', error)
      }
    }

    if (artistId) {
      fetchArtist()
    }
  }, [artistId])

  const fetchArtistExclusiveContent = async (currentArtist: ContentDetails) => {
    try {
      if (!currentArtist?.['Customer:Service:ExclusiveUrl']) return

      const exclusiveContent = await fetchAndParseContentByUrl(
        currentArtist?.['Customer:Service:ExclusiveUrl'] ?? '',
      )

      if (exclusiveContent?.data?.length) {
        setSelectedContent(exclusiveContent.data[0])
      }
    } catch (error) {
      console.error('Error fetching exclusive content:', error)
    }
  }

  const backgroundColor = colorsInfo?.secondaryBackgroundColor
  const fontColor = colorsInfo?.fontColor
  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <Card
      style={{ backgroundColor: backgroundColor || undefined }}
      className="w-full bg-background text-foreground border-0 rounded-xl overflow-hidden"
    >
      <CardHeader className="flex justify-between flex-row align-center text-center items-center py-2">
        <h2 className="md:text-xl text-sm font-semibold text-white">
          Exclusive Videos
        </h2>
        <Button
          variant="ghost"
          className="text-primary mt-0 font-sm font-medium"
          style={{
            backgroundColor: titleColor || undefined,
            borderColor: titleColor || undefined,
            color: fontColor || undefined,
            marginTop: 0,
          }}
          onClick={() => {
            if (!isUserAuthenticated) {
              setActiveTab('auth')
            } else {
              setActiveTab('exclusive')
              setActiveTabDetails({
                tab: 'exclusive',
                label: 'Exclusive Videos',
                Icon: ICON_MAP['exclusive'],
              })
            }
          }}
        >
          View All
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="h-full box-border">
          <div className="w-full h-auto">
            {!isUserAuthenticated ? (
              <div className="relative w-full h-full aspect-video">
                <AuthPlayerOverlay
                  handleClick={() => setActiveTab('auth')}
                  poster={
                    selectedContent?.['Customer:Image:LandscapeUrl'] ||
                    selectedContent?.['Content:Thumbnail:Url']
                  }
                />
              </div>
            ) : (
              <>
                <div className="relative w-full h-full aspect-video">
                  <ShakaPlayer
                    src={authorizedContentData?.[0]['Content:Url'] || ''}
                    poster={
                      selectedContent?.['Content:Landscape:Url'] ||
                      selectedContent?.['Content:Thumbnail:Url']
                    }
                    contentId={selectedContent?.['Content:Id']}
                  />
                </div>
              </>
            )}
          </div>

          {selectedContent?.['Globalization:en-US:Content:Title'] && (
            <h3
              style={{ color: fontColor || undefined }}
              className="md:text-xl text-sm mt-3 mb-0 font-semibold"
            >
              {selectedContent?.['Globalization:en-US:Content:Title']}
            </h3>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ExclusiveVideosBanner
