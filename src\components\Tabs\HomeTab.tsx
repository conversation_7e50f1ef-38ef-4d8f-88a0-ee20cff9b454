import { useEffect, useState } from 'react'
import MerchCarouselBanner from '../MerchCarouselBanner'
import CommunityBanner from '../Auth/CommunityBanner'
import JoinChatBanner from '../Auth/JoinChatBanner'
import { useUser } from '@/context/UserContext'
import { useAppContext } from '@/context/AppContext'
import { VirtualEventsListPage } from './VirtualEvents/VirtualEventsListPage'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { Skeleton } from '@/components/ui/skeleton'
import WOTE_logo from '../../assets/WOTE_logo.svg'
import ExclusiveVideosBanner from './ExclusiveVideosBanner'

interface HomeTabProps {
  slides: { id: string; image: string; title: string }[]
}

const AuthenticatedView = ({
  slides,
  loading,
}: {
  slides: { id: string; image: string; title: string }[]
  loading: boolean
}) => {
  const {
    setActiveTab,
    setSelectedProduct,
    setIsMenuOpen,
    colorsInfo,
    config,
  } = useAppContext()

  const handleProductSelect = (productId?: string | null) => {
    if (productId) {
      setSelectedProduct(productId)
    }

    setActiveTab('shopping')
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="flex flex-col space-y-4 w-full pb-4">
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        {!colorsInfo?.logo ? (
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col font-semibold relative md:bottom-1.5"
          >
            Official Community
          </h1>
        ) : (
          <img src={WOTE_logo} alt="WOTE Logo" className="h-10 w-auto" />
        )}
      </div>

      {loading ? (
        <>
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-8 mb-6"
          />
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-60 mb-10"
          />
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-120 mb-10"
          />
        </>
      ) : (
        <>
          {!titleColor ? (
            <MerchCarouselBanner
              handleProductSelect={handleProductSelect}
              slides={slides}
            />
          ) : null}
          <JoinChatBanner onJoinChat={() => setActiveTab('chat')} />
          {config?.name === 'Walk off the Earth' ? (
            <ExclusiveVideosBanner />
          ) : null}
          {config?.name !== 'Walk off the Earth' ? (
            <VirtualEventsListPage isHomeRender />
          ) : null}
        </>
      )}
    </div>
  )
}

const UnauthenticatedView = ({
  slides,
  loading,
}: {
  slides: { id: string; image: string; title: string }[]
  loading: boolean
}) => {
  const { setActiveTab, setIsMenuOpen, colorsInfo, config } = useAppContext()

  const handleProductSelect = () => {
    setActiveTab('auth')
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div>
      <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        {!colorsInfo?.logo ? (
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col font-semibold relative md:bottom-1.5"
          >
            Official Community
          </h1>
        ) : (
          <img src={WOTE_logo} alt="WOTE Logo" className="h-10 w-auto" />
        )}
      </div>

      <div className="flex flex-col items-center space-y-4 w-full">
        {loading ? (
          <>
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="bg-sidebar-widget-background w-full h-60 mb-10"
            />
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="bg-sidebar-widget-background w-full h-60 mb-10"
            />
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="bg-sidebar-widget-background w-full h-60 mb-10"
            />
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="bg-sidebar-widget-background w-full h-60"
            />
          </>
        ) : (
          <>
            <CommunityBanner
              onJoin={() => setActiveTab('auth')}
              titleKey={
                config?.name === 'Walk off the Earth'
                  ? 'APP_HOME_SIGNEDOUT_WOTE_JOIN_TITLE'
                  : 'APP_LOYALTY_SIGNEDOUT_JOINNOW_TITLE'
              }
              bodyKey={
                config?.name === 'Walk off the Earth'
                  ? 'APP_HOME_SIGNEDOUT_WOTE_JOIN_BODY'
                  : 'APP_LOYALTY_SIGNEDOUT_JOINNOW_BODY'
              }
              buttonKey="APP_LOYALTY_SIGNEDOUT_BUTTON_TEXT"
            />
            {!titleColor ? (
              <MerchCarouselBanner
                handleProductSelect={handleProductSelect}
                slides={slides}
              />
            ) : null}
            <JoinChatBanner onJoinChat={() => setActiveTab('auth')} />
            {config?.name === 'Walk off the Earth' ? (
              <ExclusiveVideosBanner />
            ) : null}
            {config?.name !== 'Walk off the Earth' ? (
              <VirtualEventsListPage isHomeRender />
            ) : null}
          </>
        )}
      </div>
    </div>
  )
}

const HomeTab = ({ slides }: HomeTabProps) => {
  const { isUserAuthenticated } = useUser()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="w-full h-full">
      {!isUserAuthenticated ? (
        <UnauthenticatedView slides={slides} loading={loading} />
      ) : (
        <AuthenticatedView slides={slides} loading={loading} />
      )}
    </div>
  )
}

export default HomeTab
