import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle, CardContent } from '@/components/ui/card'
import { useEffect, useState } from 'react'
import { shopifyClient } from '@/lib/shopify'
import { GET_PRODUCT_BY_HANDLE } from '@/queries/shopify-queries'
import BackToLink from '@/components/Shop/BackToLink'
import { useCart } from '@/context/CartContext'
import type { ProductDetailData, ProductDetailTabProps } from '@/types/shopify'
import Price from '../Shop/Price'
import Variants from '@/components/Shop/Variants'
import AddToCart from '@/components/Shop/AddToCart'
import CartButton from '../Shop/CartButton'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'

export default function ProductDetailTab({
  productId,
  onBack,
  onCartClick,
}: ProductDetailTabProps) {
  const { setIsMenuOpen, colorsInfo } = useAppContext()
  const { addToCart } = useCart()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [data, setData] = useState<ProductDetailData | null>(null)
  const [selectedVariant, setSelectedVariant] = useState<string | null>(null)
  const [selectedSize, setSelectedSize] = useState<string | null>(null)
  const [selectedColor, setSelectedColor] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProduct() {
      try {
        const response = await shopifyClient.request(GET_PRODUCT_BY_HANDLE, {
          variables: { handle: productId },
        })
        setData(response.data.product)
        // Set first variant as default selected
        if (response.data.product.variants.edges.length > 0) {
          setSelectedVariant(response.data.product.variants.edges[0].node.id)
        }
      } catch (err) {
        console.error('Error fetching product:', err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [productId])

  if (loading) return null
  if (error) return <div>Error loading product</div>
  if (!data) return null

  const formatPrice = (amount: string, currencyCode: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(parseFloat(amount))
  }

  const handleAddToCart = () => {
    if (!selectedVariant || !data) return

    const variant = data.variants.edges.find(
      (edge) => edge.node.id === selectedVariant,
    )?.node

    if (!variant) return

    addToCart({
      variantId: variant.id,
      quantity: 1,
      title: data.title,
      price: formatPrice(
        data.priceRange.minVariantPrice.amount,
        data.priceRange.minVariantPrice.currencyCode,
      ),
      image: data.images.edges[0]?.node.url,
      size: selectedSize,
      color: selectedColor,
    })

    // Remove this line to stay on the page
    // onCartClick()
  }

  const handleVariantOptionsSelect = (selectedSize: Record<string, string>) => {
    setSelectedSize(selectedSize?.['Size'])
    setSelectedColor(selectedSize?.['Color'])
  }

  const handleVariantSelect = (
    matchingVariant: string,
    selectedSize: Record<string, string>,
  ) => {
    setSelectedVariant(matchingVariant)
    handleVariantOptionsSelect(selectedSize)
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="flex-1 overflow-auto">
      <div className="flex md:gap-4 gap-2 align-center text-center mb-6">
        <button
          style={{ color: titleColor || '#22D3EE' }}
          className="flex flex-col justify-center md:hidden"
          onClick={() => setIsMenuOpen(true)}
        >
          <MobileMenuIcon />
        </button>
        <BackToLink onBack={onBack} title="Merch" />
      </div>

      <Card className="border-none bg-transparent shadow-none">
        <div className="flex justify-between items-center mb-4">
          <CardHeader className="w-full bg-transparent p-0">
            <CardTitle className="text-3xl text-neutral-50 max-w-[90%]">
              {data.title}
            </CardTitle>
          </CardHeader>
          <div className="absolute top-20 right-6 z-40">
            <CartButton onClick={onCartClick} />
          </div>
        </div>

        <CardContent className="bg-transparent px-20">
          <div className="grid grid-cols-1 gap-8">
            <div className="space-y-4">
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src={data.images.edges[0].node.url}
                  alt={data.images.edges[0].node.altText || data.title}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            <div className="flex justify-center">
              <div className="space-y-6 min-w-[305px]">
                <div className="space-y-2">
                  <Price
                    amount={data.priceRange.minVariantPrice.amount}
                    currencyCode={data.priceRange.minVariantPrice.currencyCode}
                  />
                </div>
                <div className="space-y-2">
                  <Variants
                    variants={data.variants}
                    options={data.options}
                    selectedVariant={selectedVariant}
                    onVariantSelect={handleVariantSelect}
                    onVariantOptionsSelect={handleVariantOptionsSelect}
                  />
                </div>
                <div className="space-y-4">
                  <AddToCart
                    onClick={handleAddToCart}
                    disabled={!selectedVariant}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>

        <div className="">
          <div className="text-white/80 text-sm">{data.description}</div>
        </div>
      </Card>
    </div>
  )
}
