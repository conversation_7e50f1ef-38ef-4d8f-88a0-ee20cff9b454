import { useState, useRef, ChangeEvent, useEffect } from 'react'
import { useUser } from '@/context/UserContext'
import { useAuth } from '@/context/AuthContext'
import { createContentUpload, upload } from '@/api/aws'
import { imageUploadWorkflowId } from '@/helpers/constants'
import { ContentDetails } from '@/types/content'
import { useModal } from '@/context/ModalContext'
import { removeCustomerSoft } from '@/api/user'
import { MobileMenuIcon } from '@/components/Icons/MobileMenuIcon'
import { useAppContext } from '@/context/AppContext'

import ProfilePageTab from './components/ProfilePageTab'
import BillingPageTab from './components/BillingPageTab'
import PreferencesPageTab from './components/PreferencesPageTab'
import SocialPageTab from './components/SocialPageTab'
import LogoutPageTab from './components/LogoutPageTab'
import ProfileFilters from './components/ProfileFilters'

export default function ProfileTab() {
  const { userData, handleUpdateUserData } = useUser()
  const { closeModal } = useModal()
  const { setIsMenuOpen, colorsInfo, config } = useAppContext()
  const { logout } = useAuth()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const [formUserData, setFormUserData] = useState<ContentDetails | undefined>(
    undefined,
  )
  const [activeProfileTab, setActiveProfileTab] = useState('profile')
  const [isAvatarLoading, setIsAvatarLoading] = useState(false)
  const [savedFields, setSavedFields] = useState<{ [key: string]: boolean }>({})

  const handleTabChange = (value: string) => {
    setActiveProfileTab(value)
    if (contentRef.current) {
      contentRef.current.scrollTop = 0
    }
  }

  useEffect(() => {
    if (userData) setFormUserData(userData as unknown as ContentDetails)
  }, [userData])

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormUserData(
      (prevData) =>
        ({
          ...prevData,
          [id]: value || '', // Ensure fields are initialized as strings
        }) as ContentDetails,
    )
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormUserData(
        (prevData) =>
          ({
            ...(prevData || {}),
            'Custom:User:Avatar': URL.createObjectURL(file),
          }) as ContentDetails,
      )
      uploadFile(file)
    }
  }

  const uploadFile = async (file: File): Promise<void> => {
    try {
      setIsAvatarLoading(true)
      const response = await upload({
        fileObj: file,
        assetType: 'Content',
      })

      await createContentUpload({
        ...response,
        originalTitle: file.name,
        workflowId: imageUploadWorkflowId,
        originalLanguage: 'en-US',
        localizations: {
          'en-US': { name: response.originalFileName },
        },
        type: 'Image',
        originalFileName: response.originalFileName,
        objectUrl: response.fileAssetUrl,
        publicUrl: response.cdnUrl,
        exhibitionWindow: {
          '--': {
            availableFrom:
              response.availableFrom?.toISOString &&
              response.availableFrom.toISOString(),
            availableUntil:
              response.availableUntil?.toISOString &&
              response.availableUntil.toISOString(),
          },
        },
        allowMinting: true,
        allowRemix: true,
        allowComments: true,
      })
      handleUpdateUserData({ 'Custom:User:Avatar': response.cdnUrl })
    } catch (error) {
      console.log('error:', error)
    } finally {
      setIsAvatarLoading(false)
    }
  }

  const handleAvatarClick = () => {
    fileInputRef.current?.click()
  }

  const handleInputBlur = (id: string) => {
    handleUpdateUserData({
      [id]: formUserData?.[id as keyof typeof userData],
    })
    setSavedFields((prev) => ({ ...prev, [id]: true }))

    setTimeout(() => {
      setSavedFields((prev) => ({ ...prev, [id]: false }))
    }, 2000)
  }

  const handleRemoveProfile = async () => {
    if (userData?.['Content:Id']) {
      await removeCustomerSoft(userData?.['Content:Id'])
      logout()
      closeModal()
    }
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="h-full flex flex-col w-full py-6">
      <div className="relative top-0 md:z-[999999]">
        <div className="md:sticky md:top-6 flex md:gap-4 gap-2 align-left text-left mb-6">
          <button
            style={{ color: titleColor || '#22D3EE' }}
            className="flex flex-col justify-center md:hidden"
            onClick={() => setIsMenuOpen(true)}
          >
            <MobileMenuIcon />
          </button>
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
          >
            My Account
          </h1>
        </div>

        <ProfileFilters
          activeProfileTab={activeProfileTab}
          handleTabChange={handleTabChange}
        />
      </div>

      <div className="flex flex-col min-h-[calc(100dvh-180px)] mt-6">
        <main className="flex-1 overflow-auto">
          <div ref={contentRef} className="overflow-auto p-1 mb-4">
            {activeProfileTab === 'profile' && (
              <ProfilePageTab
                isAvatarLoading={isAvatarLoading}
                handleAvatarClick={handleAvatarClick}
                formUserData={formUserData}
                handleFileChange={handleFileChange}
                fileInputRef={fileInputRef}
                handleInputBlur={handleInputBlur}
                handleInputChange={handleInputChange}
                savedFields={savedFields}
              />
            )}
            {activeProfileTab === 'billing' && (
              <BillingPageTab
                formUserData={formUserData}
                handleInputBlur={handleInputBlur}
                handleInputChange={handleInputChange}
                savedFields={savedFields}
              />
            )}
            {activeProfileTab === 'preferences' && (
              <PreferencesPageTab
                formUserData={formUserData}
                handleUpdateUserData={handleUpdateUserData}
              />
            )}
            {activeProfileTab === 'social' &&
              config?.name !== 'Walk off the Earth' && (
                <SocialPageTab
                  handleInputBlur={handleInputBlur}
                  handleInputChange={handleInputChange}
                  formUserData={formUserData}
                  userData={userData}
                  savedFields={savedFields}
                />
              )}
            {activeProfileTab === 'logout' && (
              <LogoutPageTab handleRemoveProfile={handleRemoveProfile} />
            )}
          </div>
        </main>

        <footer className="flex justify-center">
          <div className="flex flex-col justify-center items-center gap-2 p-4">
            <a
              href="https://hub.oc-club.community/legal/terms"
              className="text-sm text-orange-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms and Conditions
            </a>
            <a
              href="https://hub.oc-club.community/legal/privacy"
              className="text-sm text-orange-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Data Privacy Notice
            </a>
            <a
              href="https://hub.oc-club.community/legal/cookies"
              className="text-sm text-orange-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Cookie Policy
            </a>
          </div>
        </footer>
      </div>
    </div>
  )
}
