import { useEffect, useRef, useState } from 'react'
import { ChangeEvent } from 'react'
import { CheckCircle, LoaderCircle } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ContentDetails } from '@/types/content'
import { VITE_GOOGLE_MAPS_API_KEY } from '../../../../lib/config.json'

const BILING_FORM_DATA = [
  {
    id: 'Custom:User:Street',
    label: 'Street address',
    colSpan: 'sm:col-span-2',
    defaultValue: '',
  },
  {
    id: 'Custom:User:City',
    label: 'City / Town',
    defaultValue: '',
  },
  {
    id: 'Custom:User:State',
    label: 'State / Province / Region',
    defaultValue: '',
  },
  {
    id: 'Custom:User:Zip',
    label: 'Zip / Postal code',
    defaultValue: '',
  },
  {
    id: 'Custom:User:Country',
    label: 'Country',
    defaultValue: '',
  },
]

type BillingPageTabProps = {
  formUserData: ContentDetails | undefined
  handleInputBlur: (id: string) => void
  handleInputChange: (e: ChangeEvent<HTMLInputElement>) => void
  savedFields: { [key: string]: boolean }
}

const BillingPageTab = ({
  formUserData,
  handleInputBlur,
  handleInputChange,
  savedFields,
}: BillingPageTabProps) => {
  const [loading, setLoading] = useState(false)
  const [locationData, setLocationData] = useState<{
    [key: string]: string
  } | null>(null)

  const [formState, setFormState] = useState<{ [key: string]: string }>({})
  const hasPatchedFromUser = useRef(false)
  const hasPatchedFromLocation = useRef(false)

  useEffect(() => {
    if (formUserData && !hasPatchedFromUser.current) {
      setFormState((prev) => ({
        ...prev,
        ...formUserData,
      }))
      hasPatchedFromUser.current = true
    }
  }, [formUserData])

  useEffect(() => {
    if (locationData && !hasPatchedFromLocation.current) {
      setFormState((prev) => ({
        ...prev,
        ...locationData,
      }))
      hasPatchedFromLocation.current = true
    }
  }, [locationData])

  const handleLocalInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormState((prev) => ({ ...prev, [name]: value }))
    handleInputChange(e)
  }

  const resolveLocation = async () => {
    setLoading(true)
    // setError('')
    setLocationData(null)

    if (!navigator.geolocation) {
      // setError('Geolocation is not supported by your browser.')
      setLoading(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const lat = position.coords.latitude
        const lng = position.coords.longitude

        try {
          const apiKey = VITE_GOOGLE_MAPS_API_KEY
          const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}&language=en`,
          )
          const result = await response.json()

          if (result.status !== 'OK') {
            throw new Error('Failed to get a valid response from Google API.')
          }

          const components = result.results[0].address_components

          const getComponent = (type: string) => {
            const comp = components.find((c: any) => c.types.includes(type))
            return comp ? comp.long_name : ''
          }

          const data = {
            'Custom:User:City': getComponent('locality'),
            'Custom:User:Country': getComponent('country'),
          }

          setLocationData(data)
        } catch (err: any) {
          // setError('Failed to retrieve address: ' + err.message)
        } finally {
          setLoading(false)
        }
      },
      () => {
        // setError('Geolocation failed: ' + geoError.message)
        setLoading(false)
      },
    )
  }

  useEffect(() => {
    resolveLocation()
  }, [])

  return (
    <div className="space-y-8 text-white">
      <section>
        <h2 className="text-sm text-muted-foreground mb-4">
          Update your address details here.
        </h2>

        {loading && (
          <div className="flex justify-center items-center gap-2 text-sm text-muted-foreground mb-4">
            <LoaderCircle className="animate-spin" />
          </div>
        )}
        {/* {error && <p className="text-red-500 text-sm mb-4">{error}</p>} */}
        {!loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {BILING_FORM_DATA.map(({ id, label, colSpan }) => (
              <div key={id} className={`space-y-2 ${colSpan || ''}`}>
                <Label htmlFor={id}>{label}</Label>
                <div className="relative">
                  <Input
                    className="focus-visible:ring-primary focus-visible:border-0 text-foreground-inverse bg-foreground"
                    onBlur={() => handleInputBlur(id)}
                    onChange={handleLocalInputChange}
                    value={formState[id] || ''}
                    id={id}
                    name={id}
                  />

                  {savedFields[id] && (
                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-green-600 flex gap-2 items-center z-10 pointer-events-none bg-foreground px-2">
                      Saved
                      <CheckCircle className="text-green-600 w-[16px] h-[16px]" />
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  )
}

export default BillingPageTab
