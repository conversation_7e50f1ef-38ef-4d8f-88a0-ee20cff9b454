import ConfirmationModal from '@/components/ConfirmationModal'
import { Button } from '@/components/ui/button'
import { useAppContext } from '@/context/AppContext'
import { useAuth } from '@/context/AuthContext'
import { useModal } from '@/context/ModalContext'

type LogoutPageTab = {
  handleRemoveProfile: () => Promise<void>
}

const LogoutPageTab = ({ handleRemoveProfile }: LogoutPageTab) => {
  const { openModal, closeModal } = useModal()
  const { logout } = useAuth()
  const { colorsInfo } = useAppContext()

  // Create a handler function that properly handles the event
  const handleLogout = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    logout(true) // Pass true to clear user data
  }

  const backgroundColor = colorsInfo?.secondaryBackgroundColor
  const fontColor = colorsInfo?.fontColor

  return (
    <div>
      <div className="py-6 text-white flex justify-center items-center h-full">
        <div className="flex flex-col justify-center gap-4 w-full">
          <Button
            style={{
              backgroundColor: backgroundColor || undefined,
              color: fontColor || undefined,
              borderColor: backgroundColor || undefined,
            }}
            className="bg-black text-primary hover:bg-primary hover:text-black border border-[#27272A]"
            onClick={handleLogout}
          >
            Sign Out
          </Button>
          <Button
            className="text-destructive  hover:text-red-500 bg-transparent"
            onClick={() =>
              openModal(
                <ConfirmationModal
                  description="Removing your profile will delete your personal information and all Loyalty Points and Badges you have achieved."
                  confirmText="Yes, remove my profile and delete my progress"
                  cancelText="I’ve changed my mind, keep my profile active"
                  title="Are you sure you want to remove your profile?"
                  onConfirmClick={handleRemoveProfile}
                  onClose={closeModal}
                />,
              )
            }
            variant="secondary"
          >
            Remove Profile
          </Button>
        </div>
      </div>
    </div>
  )
}

export default LogoutPageTab
