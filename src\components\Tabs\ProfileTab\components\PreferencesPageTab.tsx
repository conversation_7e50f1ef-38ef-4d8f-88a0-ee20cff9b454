import { ContentDetails } from '@/types/content'
import { Checkbox } from '@/components/ui/checkbox'
import { UserData } from '@/types/user'
import { useAppContext } from '@/context/AppContext'

type PreferencesPageTab = {
  formUserData: ContentDetails | undefined
  handleUpdateUserData: (data: Partial<UserData>) => void
}

const PreferencesPageTab = ({
  formUserData,
  handleUpdateUserData,
}: PreferencesPageTab) => {
  const { colorsInfo } = useAppContext()

  const fontColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="space-y-8 text-white">
      <section>
        <h2 className="text-sm text-muted-foreground mb-4">
          Use this checkbox to opt in or out of our mailing list.
        </h2>
        <div className="flex items-center space-x-2">
          <Checkbox
            onCheckedChange={() => {
              const currentValue =
                formUserData?.['Customer:Settings:Notification:Email']

              handleUpdateUserData({
                'Customer:Settings:Notification:Email': [
                  '1',
                  'true',
                  true,
                ].includes(currentValue as string | boolean)
                  ? '0'
                  : '1',
              })
            }}
            checked={['1', 'true', true].includes(
              formUserData?.['Customer:Settings:Notification:Email'] as
                | string
                | boolean,
            )}
            id="newsletter"
          />

          <label
            htmlFor="newsletter"
            style={{ color: fontColor || undefined }}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground"
          >
            I want to receive newsletters and updates
          </label>
        </div>
      </section>
    </div>
  )
}

export default PreferencesPageTab
