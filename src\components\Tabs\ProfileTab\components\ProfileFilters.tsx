import { useState } from 'react'
import { Check, ChevronDown } from 'lucide-react'
import { useAppContext } from '@/context/AppContext'

const profileTabs: { value: string; label: string }[] = [
  { value: 'profile', label: 'Personal Information' },
  { value: 'social', label: 'Social Accounts' },
  { value: 'preferences', label: 'Preferences' },
  { value: 'billing', label: 'Address Details' },
  { value: 'logout', label: 'Settings' },
]

type ProfileFilters = {
  activeProfileTab: string
  handleTabChange: (value: string) => void
}

const ProfileFilters = ({
  activeProfileTab,
  handleTabChange,
}: ProfileFilters) => {
  const [open, setOpen] = useState(false)

  const { colorsInfo, config } = useAppContext()

  const backgroundColor = colorsInfo?.secondaryBackgroundColor
  const fontColor = colorsInfo?.fontColor

  const filteredTabs = profileTabs.filter((tab) => {
    if (config?.name === 'Walk off the Earth') {
      return tab.value !== 'social'
    }
    return true
  })

  return (
    <div className="inline-flex flex-col justify-start items-center w-60 relative">
      <button
        onClick={() => setOpen((prev) => !prev)}
        style={{
          backgroundColor: backgroundColor || undefined,
          color: fontColor || undefined,
        }}
        className="relative flex h-10 w-full items-center justify-between rounded-md bg-black text-primary px-3 py-2 text-sm ring-offset-background focus:outline-none"
      >
        <span className="line-clamp-1">
          {profileTabs.find((t) => t.value === activeProfileTab)?.label ||
            'Select'}
        </span>
        <ChevronDown
          style={{
            color: fontColor || undefined,
          }}
          className="h-4 w-4 opacity-50"
        />
      </button>

      {open && (
        <div
          style={{
            backgroundColor: backgroundColor || undefined,
            color: fontColor || undefined,
          }}
          className="absolute z-[9999999] mt-8 w-full max-h-96 overflow-hidden rounded-md border bg-popover text-white shadow-md border-1 border-white"
        >
          <div className="p-1">
            {filteredTabs?.map((tab) => (
              <button
                key={tab.value}
                onClick={() => {
                  handleTabChange(tab.value)
                  setOpen(false)
                }}
                style={{
                  backgroundColor: backgroundColor || undefined,
                  color: fontColor || undefined,
                }}
                className="relative cursor-pointer flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
              >
                {tab.value === activeProfileTab && (
                  <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                    <Check
                      style={{
                        color: fontColor || undefined,
                      }}
                      className="h-4 w-4 text-blue-500"
                    />
                  </span>
                )}
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default ProfileFilters
