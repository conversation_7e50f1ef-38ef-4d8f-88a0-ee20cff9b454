import { useUser } from '@/context/UserContext'
import { Loader2 } from 'lucide-react'
import { useState, useEffect } from 'react'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'
import { SingleBadgeView } from '@/components/LoyaltyMatch/SingleBadgeView'
import { MultiBadgeView } from '@/components/LoyaltyMatch/MultiBadgeView'
import { LoggedOutView } from '@/components/LoyaltyMatch/LoggedOutView'

// Dynamic view that checks badge count
const RewardsView = ({ userId }: { userId: string }) => {
  const [badgeCount, setBadgeCount] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) return

    const fetchUserBadges = async () => {
      try {
        setIsLoading(true)
        const loyaltyService = new LoyaltyMatchService()
        const url = `${loyaltyService.proxyUrl}/badgesbymember/userId/${userId}`

        console.log('Fetching user badges for layout selection:', url)
        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`Failed to fetch badges: ${await response.text()}`)
        }

        const data = await response.json()
        console.log('User badges count check:', data)

        // Extract badges from the response
        const userBadges = data.apiBadges || []

        // Filter to only include earned badges (with dateEarned)
        const earnedBadges = userBadges.filter((badge: any) => badge.dateEarned)

        setBadgeCount(earnedBadges.length)
      } catch (err) {
        console.error('Failed to fetch user badges for layout:', err)
        setError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserBadges()
  }, [userId])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-destructive text-center">
        <MultiBadgeView userId={userId} />
      </div>
    )
  }

  // Show different views based on badge count
  if (badgeCount === 1) {
    return <SingleBadgeView userId={userId} />
  } else {
    return <MultiBadgeView userId={userId} />
  }
}

export default function RewardsContent() {
  const { userData } = useUser()

  const userId = userData?.['Custom:User:Id'] || userData?.['Content:Id']

  return (
    <div className="py-6">
      {userId ? <RewardsView userId={userId} /> : <LoggedOutView />}
    </div>
  )
}
