import { useEffect, useState } from 'react'
import MerchGrid from '../MerchGreed'
import { HomeCollectionProduct } from '@/types/merch'
import ProductDetailTab from './ProductDetailTab'
import Cart from './Cart'
import SortByTypeDropDown from '@/components/Shop/SortByTypeDropDown'
import { useAppContext } from '@/context/AppContext'
import { Button } from '@/components/ui/button'
import { ShopifyErrorBoundary } from '@/components/ErrorBoundaries/ShopifyErrorBoundary'
import { useShop } from '@/context/ShopContext'
import CartButton from '../Shop/CartButton'
import { MobileMenuIcon } from '../Icons/MobileMenuIcon'
import { t } from '@/helpers'

export default function ShoppingTab() {
  const {
    selectedProduct: initialSelectedProduct,
    setSelectedProduct,
    setIsMenuOpen,
    colorsInfo,
  } = useAppContext()
  const [showCart, setShowCart] = useState(false)
  const [sortMethod, setSortMethod] = useState<string>('default')

  // Use the initialSelectedProduct from context if available
  const [localSelectedProduct, setLocalSelectedProduct] = useState<
    string | null
  >(initialSelectedProduct)

  // Get shop data from context
  const { loading, data } = useShop()

  // Clear the global selected product after using it
  useEffect(() => {
    if (initialSelectedProduct) {
      setSelectedProduct(null)
    }
  }, [initialSelectedProduct, setSelectedProduct])

  // Don't try to render without data
  if (loading) return null

  // Handle error state within ShopifyErrorBoundary
  if (!data?.collection?.products?.edges) return null

  const merchItems = data.collection.products.edges
    .filter(
      (edge: HomeCollectionProduct) =>
        edge?.node?.images?.edges[0]?.node?.url &&
        edge?.node?.title &&
        edge?.node?.handle,
    )
    .map((edge: HomeCollectionProduct) => ({
      id: edge.node.handle,
      image: edge.node.images.edges[0].node.url,
      title: edge.node.title,
      price: {
        amount: edge.node.priceRange.minVariantPrice.amount,
        currencyCode: edge.node.priceRange.minVariantPrice.currencyCode,
      },
      productType: edge.node.productType || 'Unknown',
    }))

  if (merchItems.length === 0) return null

  const filteredMerchItems = merchItems.filter((item) => {
    if (sortMethod.startsWith('type-')) {
      const productType = sortMethod.replace('type-', '')
      return item.productType === productType
    }
    return true
  })

  // Then sort the filtered items
  const sortedMerchItems = [...filteredMerchItems].sort((a, b) => {
    switch (sortMethod) {
      case 'name-asc':
        return a.title.localeCompare(b.title)
      case 'name-desc':
        return b.title.localeCompare(a.title)
      default:
        return 0
    }
  })

  if (showCart) {
    return <Cart onBack={() => setShowCart(false)} />
  }

  if (localSelectedProduct) {
    return (
      <ProductDetailTab
        productId={localSelectedProduct}
        onBack={() => setLocalSelectedProduct(null)}
        onCartClick={() => setShowCart(true)}
      />
    )
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <ShopifyErrorBoundary>
      <div
        className="hidden md:block fixed top-0 right-4 flex h-[80px] backdrop-blur-md shadow-[-2px_0_5px_rgba(0,0,0,0.1)] z-[9999]"
        style={{
          width: 'calc(100% - 70px)',
        }}
      />
      <div className="flex flex-col space-y-4 w-full pb-4">
        <div className="md:sticky md:top-6  flex justify-between mb-4 flex-col md:z-[999999]">
          <div className="flex md:gap-4 gap-2 align-left text-left mb-12 ">
            <button
              style={{ color: titleColor || '#22D3EE' }}
              className="flex flex-col justify-center md:hidden"
              onClick={() => setIsMenuOpen(true)}
            >
              <MobileMenuIcon />
            </button>
            <h1
              style={{ color: titleColor || undefined }}
              className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
            >
              {t('APP_MERCH_TITLE')}
            </h1>
          </div>

          <div className="absolute top-20 right-0 z-40 md:z-[999999]">
            <CartButton onClick={() => setShowCart(true)} />
          </div>
        </div>

        <div className="flex justify-between items-center mb-4">
          <SortByTypeDropDown onSortChange={setSortMethod} />
        </div>

        {sortedMerchItems.length > 0 ? (
          <MerchGrid
            items={sortedMerchItems}
            onProductSelect={(id) => setLocalSelectedProduct(id)}
          />
        ) : (
          <div className="overflow-auto overflow-x-hidden space-y-6">
            <div className="text-center py-10">
              <p className="text-white text-lg mb-6">
                No products match the selected filter.
              </p>
              <Button onClick={() => setSortMethod('default')}>
                Clear Filters
              </Button>
            </div>
          </div>
        )}
      </div>
    </ShopifyErrorBoundary>
  )
}
