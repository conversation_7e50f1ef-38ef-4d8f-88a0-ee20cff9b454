import { Button } from '@/components/ui/button'
import { useAppContext } from '@/context/AppContext'
import { useUser } from '@/context/UserContext'
import { ContentDetails } from '@/types/content'
import { InfoIcon } from 'lucide-react'

interface EventCardProps {
  event: ContentDetails
  onClick: (eventId: string) => void
}

const renderDataFormat = (
  date: Date | undefined,
  titleColor: string | undefined,
) => {
  if (!date) {
    return null
  }
  const month = date.toLocaleString('default', { month: 'short' })
  const day = date.getDate()
  const year = date.getFullYear()
  return (
    <>
      <h3
        style={{ color: titleColor || undefined }}
        className="text-center text-accent text-lg font-semibold leading-7"
      >
        {month}
      </h3>
      <h3
        style={{ color: titleColor || undefined }}
        className="text-center text-accent text-lg font-semibold leading-7"
      >
        {day},
      </h3>
      <h3
        style={{ color: titleColor || undefined }}
        className="text-center text-accent text-lg font-semibold leading-7"
      >
        {year}
      </h3>
    </>
  )
}

export function VirtualEventCard({ event, onClick }: EventCardProps) {
  const { isUserAuthenticated } = useUser()
  const { setActiveTab, colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const secondaryText = colorsInfo?.secondaryText

  return (
    <div className="flex flex-col items-center rounded-lg justify-between py-4 px-2 pb-4">
      <div className="w-full md:text-left text-center">
        <div
          style={{ borderColor: titleColor || undefined }}
          className="flex gap-2 border-b-2 border-accent md:text-left text-center md:justify-start justify-center py-2"
        >
          {renderDataFormat(
            event['Content:ReleaseDate']
              ? new Date(event['Content:ReleaseDate'])
              : undefined,
            titleColor,
          )}
        </div>
      </div>

      <div className="flex md:flex-row flex-col justify-between w-full">
        <div className={`py-6 rounded-lg ml-1 min-w-[215px] bg-transparent`}>
          {event?.['Globalization:en-US:Content:Title'] && (
            <h3
              style={{ color: secondaryText || undefined }}
              className="text-lg font-medium md:text-left text-center pt-2"
            >
              {event?.['Globalization:en-US:Content:Title']}
            </h3>
          )}
          <div className="flex md:flex-col flex-row gap-2 justify-center mt-2">
            <p
              style={{ color: secondaryText || undefined }}
              className="text-base md:text-left text-left"
            >
              ⚆{' '}
              {event['Content:ReleaseDate'] &&
                new Date(event['Content:ReleaseDate']).toLocaleString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                })}
            </p>
          </div>
        </div>
        <div className="pt-4 px-1 flex md:flex-col justify-center align-center text-center">
          <Button
            onClick={() => {
              if (!isUserAuthenticated) {
                setActiveTab('auth')
              } else {
                onClick(event['Content:Id'])
              }
            }}
            variant="outline"
            size="lg"
            className="w-40 h-11 rounded-md"
            style={{
              backgroundColor: titleColor || undefined,
              borderColor: titleColor || undefined,
            }}
          >
            <InfoIcon />
            Event Details
          </Button>
        </div>
      </div>
    </div>
  )
}
