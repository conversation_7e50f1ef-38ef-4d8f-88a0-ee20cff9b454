import useContentAuthorize from '@/hooks/useContentAuthorize'
import useContent from '@/hooks/useContent'
import { ContentDetails } from '@/types/content'
import { useEffect } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import ShakaPlayer from '@/components/Player/ShakaPlayer'
import { TopBreadcrumb } from '@/components/TopBreadcrumb'
import LiveChat from '@/components/Chat/LiveChat'
import { useAppContext } from '@/context/AppContext'
import AuthPlayerOverlay from '@/components/Player/AuthPlayerOverlay'
import { useModal } from '@/context/ModalContext'
import SubscriptionComponent from '@/components/Subscription/SubscriptionComponent'
import { useUser } from '@/context/UserContext'
import { Button } from '@/components/ui/button'
import { TicketIcon } from 'lucide-react'
import { MobileMenuIcon } from '@/components/Icons/MobileMenuIcon'
import MerchCarouselBanner from '@/components/MerchCarouselBanner'
import useContentHitContract from '@/hooks/useContentHitContract'
import useWebsocket from '@/hooks/useWebsocket'

export const VirtualEventPlayerPage = ({
  slides,
}: {
  slides: { id: string; image: string; title: string }[]
}) => {
  const {
    activeTabContent,
    setActiveTab,
    setIsMenuOpen,
    setSelectedProduct,
    isContentPaymentUpdated,
    setIsContentPaymentUpdated,
    colorsInfo,
    loyaltyMatchMerchantId,
  } = useAppContext()

  const virtualEventId = activeTabContent?.['Content:Id']
  const { openModal } = useModal()
  const { content, isLoading } = useContent(virtualEventId)
  const { isUserAuthenticated } = useUser()

  useWebsocket()

  useContentHitContract(content)

  const {
    isLoading: authContentLoading,
    data: authorizedContentData,
    errorCode,
    refetch,
    error,
  } = useContentAuthorize(content ? [content as ContentDetails] : [])

  useEffect(() => {
    if (errorCode === '401') refetch()
  }, [errorCode, refetch])

  useEffect(() => {
    if (isContentPaymentUpdated) {
      refetch()
      setIsContentPaymentUpdated(false)
    }
  }, [isContentPaymentUpdated, refetch])

  const handleProductSelect = (productId?: string | null) => {
    if (productId) {
      setSelectedProduct(productId)
    }

    setActiveTab('shopping')
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor
  const backgroundColor = colorsInfo?.backgroundColor
  const fontColor = colorsInfo?.fontColor

  if (isLoading || authContentLoading) {
    return (
      <div className="relative p-10 w-full">
        <Skeleton
          style={{ backgroundColor: titleColor || undefined }}
          className="w-40 h-10 mb-10"
        />
        <div className="flex flex-col md:flex-row gap-10 h-full">
          <div className="flex flex-col w-full gap-10">
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="w-full h-[16rem]"
            />
            <Skeleton
              style={{ backgroundColor: titleColor || undefined }}
              className="w-full h-[16rem]"
            />
          </div>
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="w-full md:max-w-[322px] h-[rem]"
          />
        </div>
      </div>
    )
  }

  return (
    <div
      className={`md:z-[999999] md:h-fit flex md:flex-row flex-col w-full justify-between justify-between ${authorizedContentData?.[0]?.['Content:Url'] && 'md:flex-row w-full'}`}
    >
      <div className="w-full flex md:flex-row flex-col">
        <div className="w-full md:w-[70%] lg:w-[60%] px-6 md:h-screen">
          <div className="flex md:gap-4 gap-2 align-left text-left ">
            <button
              style={{ color: titleColor || '#22D3EE' }}
              className="flex flex-col justify-center md:hidden"
              onClick={() => setIsMenuOpen(true)}
            >
              <MobileMenuIcon />
            </button>
            <TopBreadcrumb
              redirectTab={{ tab: 'virtual-events', title: 'Virtual Events' }}
            />
          </div>

          {isLoading ? (
            <Skeleton className="w-40 h-6 mb-3" />
          ) : (
            <h1
              style={{ color: titleColor || undefined }}
              className="mb-3 md:text-3xl text-2xl font-bold text-left relative md:bottom-1.5"
            >
              {content?.['Globalization:en-US:Content:Title']}
            </h1>
          )}
          <div className="w-full mb-4">
            {error === 'ERROR_UNAUTHORIZED' && (
              <div className="relative w-full h-full aspect-video">
                <AuthPlayerOverlay
                  handleClick={() => {
                    if (!isUserAuthenticated) {
                      setActiveTab('auth')
                    } else {
                      openModal(
                        <SubscriptionComponent
                          title={content?.['Globalization:en-US:Content:Title']}
                          contentId={content?.['Content:Id']}
                        />,
                      )
                    }
                  }}
                  poster={
                    content?.['Customer:Image:LandscapeUrl'] ||
                    content?.['Content:Thumbnail:Url']
                  }
                />
              </div>
            )}

            {error && error !== 'ERROR_UNAUTHORIZED' && (
              <div className="relative w-full h-full aspect-video">
                <AuthPlayerOverlay
                  handleClick={() => {
                    if (!isUserAuthenticated) {
                      setActiveTab('auth')
                    }
                  }}
                  poster={
                    content?.['Customer:Image:LandscapeUrl'] ||
                    content?.['Content:Thumbnail:Url']
                  }
                />
              </div>
            )}

            {authorizedContentData?.[0]?.['Content:Url'] && (
              <ShakaPlayer
                src={authorizedContentData[0]['Content:Url']}
                poster={
                  content?.['Content:Landscape:Url'] ||
                  content?.['Content:Thumbnail:Url']
                }
                contentId={content?.['Content:Id']} // Use content instead, which has the proper type
                loyaltyMatchMerchantId={loyaltyMatchMerchantId}
              />
            )}
          </div>

          {!authorizedContentData?.[0]?.['Content:Url'] && (
            <>
              <div className="space-y-8">
                <div
                  style={{ color: titleColor || undefined }}
                  className="flex flex-row gap-5 md:gap-11 items-start"
                >
                  {content?.['Content:ReleaseDate'] && (
                    <div>
                      <div
                        style={{ color: titleColor || undefined }}
                        className="text-sm text-foreground"
                      >
                        Date
                      </div>
                      <div className="text-lg font-bold">
                        {new Date(
                          content?.['Content:ReleaseDate'],
                        ).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </div>
                    </div>
                  )}
                  {content?.['Content:ReleaseDate'] && (
                    <div style={{ color: titleColor || undefined }}>
                      <div
                        style={{ color: titleColor || undefined }}
                        className="text-sm text-foreground"
                      >
                        Time
                      </div>
                      <div className="text-lg font-bold flex gap-2">
                        {new Date(
                          content?.['Content:ReleaseDate'],
                        ).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true,
                          timeZone: 'America/New_York',
                        })}

                        <div className="text-lg font-bold">
                          {content?.['Content:ReleaseDate:TimeZone']}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {!authorizedContentData?.[0]?.['Content:Url'] && (
            <div
              style={{ color: titleColor || undefined }}
              className="flex flex-col gap-2 block md:hidden"
            >
              {content?.['Product:Price:Amount'] && (
                <div className="flex md:justify-between gap-10 my-4">
                  <div className="whitespace-nowrap">
                    <div className="text-sm mb-2 font-semibold">Tickets</div>
                    <div className="text-lg font-bold flex gap-2">
                      <p>{content?.['Product:Price:Amount']}</p>
                      <p> {content?.['Product:Price:Currency']}</p>
                    </div>
                  </div>
                </div>
              )}

              <Button
                className="w-full h-11 px-8 py-2 rounded-md inline-flex justify-center items-center gap-2 text-zinc-900 bg-cyan-400"
                onClick={() => {
                  if (!isUserAuthenticated) {
                    setActiveTab('auth')
                  } else {
                    openModal(
                      <SubscriptionComponent
                        title={content?.['Globalization:en-US:Content:Title']}
                        contentId={content?.['Content:Id']}
                      />,
                    )
                  }
                }}
                variant="default"
                size="lg"
              >
                <TicketIcon />
                Buy Access
              </Button>
            </div>
          )}

          <div className="mt-4 flex md:flex-row flex-col gap-4 justify-between">
            <div className="flex flex-col gap-4 max-w-[100%]">
              {!authorizedContentData?.[0]?.['Content:Url'] && (
                <div
                  style={{ color: titleColor || undefined }}
                  className="text-sm text-foreground font-semibold"
                >
                  Livestream Details
                </div>
              )}
              {content?.['Globalization:en-US:Content:Description'] && (
                <span
                  style={{ color: titleColor || undefined }}
                  dangerouslySetInnerHTML={{
                    __html:
                      content?.['Globalization:en-US:Content:Description'] ||
                      '',
                  }}
                  className="text-sm w-full"
                />
              )}

              <div className="hidden md:block">
                {authorizedContentData?.[0]?.['Content:Url'] && (
                  <MerchCarouselBanner
                    handleProductSelect={handleProductSelect}
                    slides={slides}
                    className="bg-sidebar-widget-background text-xl font-semibold"
                  />
                )}
              </div>
            </div>

            {!authorizedContentData?.[0]?.['Content:Url'] && (
              <div
                style={{ color: titleColor || undefined }}
                className="gap-16 flex flex-col md:flex-row justify-end"
              >
                {content?.['Product:Price:Amount'] && (
                  <div className="flex md:justify-between gap-10 hidden md:block">
                    <div className="whitespace-nowrap">
                      <div className="text-sm mb-2 font-semibold">Tickets</div>
                      <div className="text-lg font-bold flex gap-2">
                        <p>{content?.['Product:Price:Amount']}</p>
                        <p> {content?.['Product:Price:Currency']}</p>
                      </div>
                    </div>
                  </div>
                )}
                <div className="flex flex-col gap-2 hidden md:block">
                  <Button
                    onClick={() => {
                      if (!isUserAuthenticated) {
                        setActiveTab('auth')
                      } else {
                        openModal(
                          <SubscriptionComponent
                            title={
                              content?.['Globalization:en-US:Content:Title']
                            }
                            contentId={content?.['Content:Id']}
                          />,
                        )
                      }
                    }}
                    variant="default"
                    className="w-full md:w-40"
                    size="lg"
                    style={{
                      backgroundColor: titleColor || undefined,
                      borderColor: titleColor || undefined,
                      color: fontColor || undefined,
                    }}
                  >
                    <TicketIcon />
                    Buy Access
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div
          style={{
            backgroundColor: titleColor || undefined,
            borderColor: titleColor || undefined,
          }}
          className={`${!!titleColor ? 'md:h-4/5 md:mt-20 md:rounded-xl' : 'md:h-full'} w-full md:w-[30%] lg:w-[40%] bg-sidebar-hub-background px-3 md:pb-0 pb-4 md:m-0 my-4`}
        >
          <h1 className="mt-2 invisible hidden md:block">hidden block</h1>
          <div
            className={`${!!titleColor ? 'md:h-4/5' : 'md:h-4/5'} h-full w-full p-3 rounded-xl`}
          >
            {content?.['Content:Id'] && (
              <div className="h-full">
                <h4
                  style={{ color: backgroundColor || undefined }}
                  className="mb-6 text-2xl font-semibold"
                >
                  Event Chat
                </h4>
                <LiveChat roomId={content?.['Content:Id']} title="" />
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="block md:hidden p-0">
        <MerchCarouselBanner
          handleProductSelect={handleProductSelect}
          slides={slides}
          className="bg-sidebar-widget-background text-xl font-semibold"
        />
      </div>
    </div>
  )
}

export default VirtualEventPlayerPage
