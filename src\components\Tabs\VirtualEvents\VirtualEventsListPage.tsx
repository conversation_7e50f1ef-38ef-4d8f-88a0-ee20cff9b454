import { useEffect } from 'react'
import { VirtualEventCard } from './VirtualEventCard'
import { useFetchPageCatalog } from '@/hooks/useFetchPageCatalog'
import { useAppContext } from '@/context/AppContext'
import { ContentDetails } from '@/types/content'
import { Skeleton } from '@/components/ui/skeleton'
import { MobileMenuIcon } from '@/components/Icons/MobileMenuIcon'
import { TokenRefresher } from '@/components/TokenRefresher'

export const VirtualEventsListPage = ({
  isHomeRender,
}: {
  isHomeRender?: boolean
}) => {
  const {
    setActiveTab,
    setActiveTabContent,
    setIsMenuOpen,
    artistId,
    colorsInfo,
  } = useAppContext()

  const {
    data: ECList,
    loading,
    error,
  } = useFetchPageCatalog(
    'https://cdn.oc-club.community/api/v6/catalog/7df12038-390c-4382-baf8-196c1576dbaa/children/catalog/entity.json',
    isHomeRender,
  )

  const upcomingEvents = ECList?.filter((ec) => ec.carouselTitle === 'Upcoming')

  useEffect(() => {
    console.log('artistId Virtual events list page', artistId)
  }, [artistId])

  const sortedUpcomingEvents = upcomingEvents?.map((ec) => ({
    ...ec,
    data: [...ec.data]
      .sort(
        (a, b) =>
          new Date(a['Content:ReleaseDate']).getTime() -
          new Date(b['Content:ReleaseDate']).getTime(),
      )
      .slice(isHomeRender ? -3 : undefined),
  }))

  if (error) {
    return null
  }

  const handleCardClick = (event: ContentDetails) => {
    setActiveTabContent(event)

    setActiveTab('virtual-event-details')
  }

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <div className="w-full ">
      <TokenRefresher />
      {isHomeRender ? (
        <div className="w-full">
          <h4
            style={{ color: titleColor || undefined }}
            className="mt-2 d:text-3xl text-xl font-semibold"
          >
            Virtual Events
          </h4>
        </div>
      ) : (
        <div className="md:sticky md:top-6 md:z-[999999] flex md:gap-4 gap-2 align-left text-left mb-6">
          <button
            style={{ color: titleColor || '#22D3EE' }}
            className="flex flex-col justify-center md:hidden"
            onClick={() => setIsMenuOpen(true)}
          >
            <MobileMenuIcon />
          </button>
          <h1
            style={{ color: titleColor || undefined }}
            className="md:text-3xl text-2xl flex flex-col justify-center font-semibold relative md:bottom-1.5"
          >
            Virtual Events
          </h1>
        </div>
      )}

      {loading ? (
        <div className="w-full">
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-8 mb-6 mt-2"
          ></Skeleton>
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-60 mb-10"
          ></Skeleton>
          <Skeleton
            style={{ backgroundColor: titleColor || undefined }}
            className="bg-sidebar-widget-background w-full h-60"
          ></Skeleton>
        </div>
      ) : (
        <div className="space-y-0">
          {(isHomeRender ? sortedUpcomingEvents : ECList)?.map((ec) =>
            ec.data.map((event) => (
              <VirtualEventCard
                onClick={() => handleCardClick(event)}
                key={event['Content:Id']}
                event={event}
                // isHomeRender={isHomeRender}
              />
            )),
          )}
        </div>
      )}
    </div>
  )
}
