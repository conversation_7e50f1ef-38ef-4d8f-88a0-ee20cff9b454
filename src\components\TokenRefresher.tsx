import { useEffect, useState } from 'react'
import { useTokenRefresh } from '../hooks/useTokenRefresh'
import { jwtDecode } from 'jwt-decode'
import { useAppContext } from '@/context/AppContext'

export function TokenRefresher() {
  const { refreshToken, error } = useTokenRefresh()
  const [refreshAttempted, setRefreshAttempted] = useState(true)
  const { setActiveTab } = useAppContext()

  useEffect(() => {
    const attemptRefresh = async () => {
      if (!refreshAttempted) return

      const currentPath = window.location.pathname
      console.log(
        `🔄 TokenRefresher: Starting token refresh check on ${currentPath}`,
      )
      logTokenExpiration()

      // Check token status before attempting refresh
      console.log('🔄 TokenRefresher: Attempting to refresh token...')
      const success = await refreshToken()

      if (success) {
        console.log('✅ TokenRefresher: Token refresh successful')
        logTokenExpiration()
        // Clear the authRequired flag
        localStorage.removeItem('authRequired')

        setRefreshAttempted(false)
      } else {
        console.error('❌ TokenRefresher: Token refresh failed', error)

        // If token refresh fails, redirect to auth tab instead of using navigate
        console.log(
          `🔀 TokenRefresher: Redirecting to auth from ${currentPath}`,
        )

        // Store the current path or tab if needed for returning later
        localStorage.setItem(
          'returnTab',
          localStorage.getItem('activeTab') || 'home',
        )

        // Redirect to auth tab
        setActiveTab('auth')

        setRefreshAttempted(false)
      }
    }

    // Check if 'authRequired' flag is set
    const authRequired = localStorage.getItem('authRequired') === 'true'

    if (authRequired) {
      console.log(
        '🔑 TokenRefresher: authRequired flag detected, will attempt refresh',
      )
      attemptRefresh()
    } else {
      console.log(
        '📄 TokenRefresher: No token refresh needed on this page load',
      )
    }

    return () => {
      console.log('🔄 TokenRefresher: Component unmounting')
    }
  }, [refreshToken, error, setActiveTab, refreshAttempted])

  return null // This component doesn't render anything
}

const logTokenExpiration = (tokenName = 'hubUserToken') => {
  const token =
    localStorage.getItem(tokenName) ||
    localStorage.getItem(
      tokenName === 'hubUserToken' ? 'widgetUserToken' : 'hubUserToken',
    )

  if (!token) {
    console.warn('⚠️ TokenRefresher: No token found in localStorage.')
    return
  }

  try {
    const decoded = jwtDecode<{ exp: number }>(token)
    const expTimestamp = new Date(decoded.exp * 1000)
    const formattedExpiration = expTimestamp.toISOString()

    console.log('🔐 TokenRefresher: Token will expire at:', formattedExpiration)

    const now = Math.floor(Date.now() / 1000)
    const timeLeft = decoded.exp - now
    const hoursLeft = Math.floor(timeLeft / 3600)
    const minutesLeft = Math.floor((timeLeft % 3600) / 60)

    console.log(
      `🕒 TokenRefresher: Token expires in: ${hoursLeft}h ${minutesLeft}m (${timeLeft} seconds)`,
    )

    localStorage.setItem('tokenExpiration', formattedExpiration)
  } catch (err) {
    console.error('❌ TokenRefresher: Failed to decode token:', err)
  }
}
