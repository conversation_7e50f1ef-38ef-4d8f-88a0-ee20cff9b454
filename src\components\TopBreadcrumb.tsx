import { useAppContext } from '@/context/AppContext'
import { Tab } from '@/types/navigation'
import { ChevronLeft } from 'lucide-react'

type RedirectTabType = {
  title: string
  tab: Tab
}

type TopBreadcrumbProps = {
  redirectTab: RedirectTabType
}

export function TopBreadcrumb({ redirectTab }: TopBreadcrumbProps) {
  const { setActiveTab, colorsInfo } = useAppContext()

  const titleColor = colorsInfo?.secondaryBackgroundColor

  return (
    <nav className="py-4 pt-6 flex">
      <div
        style={{ color: titleColor || undefined }}
        className="flex items-center gap-2 text-xl font-medium text-base/accent-6 hover:underline"
        onClick={() => setActiveTab(redirectTab.tab)}
      >
        <ChevronLeft className="w-4 h-4" />
        {redirectTab.title}
      </div>
    </nav>
  )
}
