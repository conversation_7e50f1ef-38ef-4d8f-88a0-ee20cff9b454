import { ArtistConfig } from '@/types/artist'

export const isDevelopment =
  window.location.href.includes('oc-club.community') ||
  window.location.href.includes('http://localhost:5174') ||
  window.location.href.includes('http://localhost:5173') ||
  import.meta.env.DEV

export const FALLBACK_ARTIST_ID = '172da643-0a04-46c5-ab34-aa48d00ded31'

export const DEFAULT_ARTIST_CONFIG: ArtistConfig = {
  id: '',
  name: '',
  slug: 'default',
  colors: null,
  menu: null,
  merchantId: undefined,
}
