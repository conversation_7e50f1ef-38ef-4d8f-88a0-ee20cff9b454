import {
  type ReactNode,
  createContext,
  useContext,
  useMemo,
  useState,
  useCallback,
  useEffect,
} from 'react'
import { GET_FEATURED_ARTISTS } from '@/queries/wordpress-queries'
import {
  Artist,
  ArtistColorsConfig,
  ArtistConfig,
  ConfigMenuProps,
  FeaturedArtist,
} from '@/types/artist'
import { useQuery } from '@apollo/client'
import { Tab } from '@/types/navigation'
import { ContentDetails } from '@/types/content'
import { useAuth } from './AuthContext'
import { DEFAULT_SLUG, loadArtistConfigBySlug } from '@/lib/artistConfig'
import { DEFAULT_ARTIST_CONFIG } from '@/constants'

type AppContextType = {
  artistId: string | null
  setArtistId: (artistId: string | null) => void
  activeTabContent: ContentDetails | undefined
  formattedFeaturedArtistData: Artist[]
  sidebarTitle: string
  error: string | null
  loading: boolean
  activeTab: Tab
  setActiveTabContent: (content: ContentDetails) => void
  setSidebarTitle: (title: string) => void
  setActiveTab: (tab: Tab) => void
  selectedProduct: string | null
  setSelectedProduct: (productId: string | null) => void
  isMenuOpen: boolean
  setIsMenuOpen: (visible: boolean) => void
  isMenuHovered: boolean
  setIsMenuHovered: (visible: boolean) => void
  sessionExpiredMessage: string | null
  setSessionExpiredMessage: (message: string | null) => void
  config: ArtistConfig
  setConfig: React.Dispatch<React.SetStateAction<ArtistConfig>>
  isContentPaymentUpdated: boolean
  setIsContentPaymentUpdated: (visible: boolean) => void
  colorsInfo: ArtistColorsConfig | null
  setColorsInfo: React.Dispatch<React.SetStateAction<ArtistColorsConfig | null>>
  activeTabDetails: ConfigMenuProps | null
  setActiveTabDetails: React.Dispatch<
    React.SetStateAction<ConfigMenuProps | null>
  >
  loyaltyMatchMerchantId?: string // Add optional merchant ID
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [sidebarTitle, setSidebarTitle] = useState<string>('')
  const [activeTab, setActiveTabState] = useState<Tab>('home')
  const [activeTabDetails, setActiveTabDetails] =
    useState<ConfigMenuProps | null>(null)
  const [artistId, setArtistId] = useState<string | null>(null)
  const [activeTabContent, setActiveTabContent] = useState<
    ContentDetails | undefined
  >(undefined)
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [sessionExpiredMessage, setSessionExpiredMessage] = useState<
    string | null
  >(null)

  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isMenuHovered, setIsMenuHovered] = useState(false)
  const [isContentPaymentUpdated, setIsContentPaymentUpdated] = useState(false)
  const [config, setConfig] = useState<ArtistConfig>(DEFAULT_ARTIST_CONFIG)

  const [colorsInfo, setColorsInfo] = useState<ArtistColorsConfig | null>(null)

  // Use a try/catch to handle the case where we might be rendered before AuthProvider
  let auth: any
  try {
    auth = useAuth()
  } catch (e) {
    // Auth context not available yet, provide fallbacks
    auth = {
      isAuthRequired: () => false,
      sessionExpired: false,
    }
  }

  // Enhanced setActiveTab function that checks auth requirements
  const setActiveTab = useCallback(
    (tab: Tab) => {
      // Check if the tab requires authentication and session is expired
      if (
        auth.isAuthRequired &&
        auth.isAuthRequired(tab) &&
        auth.sessionExpired
      ) {
        // Set a message explaining why they're being redirected
        setSessionExpiredMessage(
          'Your session has expired. Please log in again to continue.',
        )
        // Redirect to auth tab
        setActiveTabState('auth')
      } else {
        // Normal tab change
        setActiveTabState(tab)
      }
    },
    [auth],
  )

  // Check session status on mount
  useEffect(() => {
    if (auth.sessionExpired) {
      // If current tab requires auth, redirect
      if (auth.isAuthRequired && auth.isAuthRequired(activeTab)) {
        setSessionExpiredMessage(
          'Your session has expired. Please log in again to continue.',
        )
        setActiveTabState('auth')
      }
    }
  }, [auth.sessionExpired, activeTab, auth.isAuthRequired])

  const {
    loading: artistDataLoading,
    error: artistDataError,
    data: artistData,
  } = useQuery(GET_FEATURED_ARTISTS)

  const formattedFeaturedArtistData: Artist[] = useMemo(
    () =>
      artistData?.featuredArtists?.edges
        ? artistData.featuredArtists.edges
            .filter(
              (edge: FeaturedArtist) =>
                edge?.node?.featuredArtistIDACF?.featuredArtistId &&
                edge?.node?.featuredImage?.node?.mediaItemUrl &&
                edge?.node?.title,
            )
            .map((edge: FeaturedArtist) => ({
              id: edge.node.featuredArtistIDACF.featuredArtistId,
              image: edge.node.featuredImage.node.mediaItemUrl,
              title: edge.node.title,
              leapId: edge.node.featuredArtistIDACF.leapId,
              loyaltyMatchMerchantId:
                edge.node.featuredArtistIDACF.loyaltyMatchMerchantId,
              consoleArtistId: edge.node.featuredArtistIDACF.consoleArtistId,
              memberText: edge.node.featuredArtistIDACF.memberText,
              shopifyId: edge.node.featuredArtistIDACF.shopifyId,
              shopifyFeaturedCollectionId:
                edge.node.featuredArtistIDACF.shopifyFeaturedCollectionId,
              bannerImage:
                edge.node.featuredArtistIDACF.bannerImage?.node?.mediaItemUrl,
              newsId: edge.node.featuredArtistIDACF.newsId,
              featuredPostId: edge.node.featuredArtistIDACF.featuredPostId,
            }))
        : [], // Return empty array if no data yet
    [artistData],
  )

  useEffect(() => {
    const loadConfig = async () => {
      const foundArtist = formattedFeaturedArtistData.find(
        (artist) => artist.id === DEFAULT_SLUG,
      )

      const slug = foundArtist?.id || DEFAULT_SLUG
      const result = await loadArtistConfigBySlug(slug)

      setConfig(result)
    }

    loadConfig()
  }, [formattedFeaturedArtistData])

  // Get merchant ID from config
  const loyaltyMatchMerchantId = config?.merchantId

  const contextValue = useMemo(
    () => ({
      error: artistDataError ? artistDataError.message : null,
      formattedFeaturedArtistData,
      loading: artistDataLoading,
      sidebarTitle,
      setActiveTabContent,
      activeTabContent,
      setSidebarTitle,
      setActiveTab,
      activeTab,
      selectedProduct,
      setSelectedProduct,
      isMenuOpen,
      setIsMenuOpen,
      isMenuHovered,
      setIsMenuHovered,
      setArtistId,
      artistId,
      sessionExpiredMessage,
      setSessionExpiredMessage,
      config,
      setConfig,
      isContentPaymentUpdated,
      setIsContentPaymentUpdated,
      colorsInfo,
      setColorsInfo,
      activeTabDetails,
      setActiveTabDetails,
      loyaltyMatchMerchantId,
    }),
    [
      sidebarTitle,
      setSidebarTitle,
      formattedFeaturedArtistData,
      artistDataLoading,
      artistDataError,
      setActiveTab,
      activeTab,
      selectedProduct,
      setSelectedProduct,
      activeTabContent,
      setActiveTabContent,
      isMenuOpen,
      setIsMenuOpen,
      isMenuHovered,
      setIsMenuHovered,
      setArtistId,
      artistId,
      sessionExpiredMessage,
      setSessionExpiredMessage,
      config,
      setConfig,
      isContentPaymentUpdated,
      setIsContentPaymentUpdated,
      colorsInfo,
      setColorsInfo,
      activeTabDetails,
      setActiveTabDetails,
    ],
  )

  return (
    <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>
  )
}

export const useAppContext = () => {
  const context = useContext(AppContext)
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}
