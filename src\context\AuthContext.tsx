import {
  createContext,
  useCallback,
  useContext,
  ReactNode,
  useState,
  useMemo,
  useEffect,
} from 'react'
import { authenticateUser, registerUserAction } from '@/api/auth'
import { useUser } from './UserContext'
import { transformResponse } from '@/helpers'
import { AuthSocialData } from '@/types/auth'
import { UserData } from '@/types/user'
import { useAppContext } from './AppContext'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'
import { PropertiesType } from '@/types/app'

// Enhanced helper function to register and update user email in LoyaltyMatch
const registerWithLoyaltyMatch = async (userData: UserData): Promise<any> => {
  const userId = userData['Custom:User:Id'] || userData['Content:Id']
  // Get the email from user data
  const email = userData['Custom:User:Email'] || undefined

  if (!userId) {
    console.log('❌ No userId available for LoyaltyMatch registration')
    return
  }

  console.log(
    '🔑 Registering user with Loyalty<PERSON>atch userId:',
    userId,
    'email:',
    email,
  )

  try {
    // Check if user already exists first
    try {
      const loyaltyService = new LoyaltyMatchService()
      console.log(
        'Checking if user exists URL:',
        `${loyaltyService.proxyUrl}/members?userId=${userId}`,
      )
      const response = await fetch(
        `${loyaltyService.proxyUrl}/members?userId=${userId}`,
      )
      const data = await response.json()

      if (data.apiMembers && data.apiMembers.length > 0) {
        console.log('✅ User already exists in LoyaltyMatch:', userId)

        // If user exists and we have an email, update their account with it
        if (email && email.includes('@')) {
          // Make sure it looks like an email
          console.log(
            '📧 Updating existing LoyaltyMatch user with email:',
            email,
          )
          try {
            console.log('Calling loyaltyService.updateMember with:', {
              userId,
              email,
            })
            const updateResult = await loyaltyService.updateMember({
              userId,
              email,
            })
            console.log('📧 Email update result:', updateResult)
            return { existing: true, updated: true }
          } catch (updateError) {
            console.error(
              'Failed to update email:',
              updateError,
              'Error type:',
              typeof updateError,
              'Error name:',
              updateError instanceof Error ? updateError.name : 'not an Error',
            )
          }
        } else {
          console.log('⚠️ No valid email found in user data:', email)
        }

        return { existing: true }
      }
    } catch (checkError) {
      console.log(
        'Unable to verify if user exists in LoyaltyMatch:',
        checkError,
      )
    }

    // User doesn't exist - proceed with registration including email if available
    console.log(
      'Proceeding with LoyaltyMatch registration for:',
      userId,
      'with email:',
      email,
    )

    const loyaltyService = new LoyaltyMatchService()
    const result = await loyaltyService.registerUser({
      userId,
      email,
    })

    console.log('✅ LoyaltyMatch registration successful:', result)
    return result
  } catch (error) {
    console.error('⚠️ LoyaltyMatch registration failed:', error)
    // Don't throw - we don't want auth to fail if loyalty registration fails
    return { error }
  }
}

interface AuthContextType {
  login: (credentials: AuthSocialData) => Promise<{
    success: boolean
    errorCode?: string
    errorMessage?: string
    properties?: PropertiesType[]
  }>
  registerUser: (
    userData: UserData & AuthSocialData,
  ) => Promise<{ success: boolean; errorCode?: string; errorMessage?: string }>
  logout: (preserveData?: boolean) => void
  authLoading: boolean
  sessionExpired: boolean
  isAuthRequired: (routeName?: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const { setActiveTab } = useAppContext()
  const { fetchUserData, setUserData } = useUser()
  const [authLoading, setAuthLoading] = useState(false)
  const [sessionExpired, setSessionExpired] = useState(false)

  // Check if session is expired on mount
  useEffect(() => {
    const isExpired = localStorage.getItem('widgetSessionExpired') === 'true'
    setSessionExpired(isExpired)
  }, [])

  const setUserToLocalStorage = (data: UserData): void => {
    localStorage.setItem('widgetUserToken', data['Custom:User:Token'])
    localStorage.setItem('widgetUserId', data['Custom:User:Id'])
    localStorage.setItem('widgetUserData', JSON.stringify(data))
    // Clear any session expired flag when setting new user data
    localStorage.removeItem('widgetSessionExpired')
    setSessionExpired(false)
  }

  // Auth-required routes - add any routes that require authentication
  const authRequiredRoutes = useMemo(
    () => ['profile', 'chat', 'subscription', 'loyalty'],
    [],
  )

  // Check if a route requires authentication
  const isAuthRequired = useCallback(
    (routeName?: string) => {
      if (!routeName) return false
      return authRequiredRoutes.includes(routeName.toLowerCase())
    },
    [authRequiredRoutes],
  )

  const login = useCallback(
    async (
      credentials: AuthSocialData,
    ): Promise<{
      errorMessage?: string
      errorCode?: string
      success: boolean
      properties?: PropertiesType[]
    }> => {
      setAuthLoading(true)
      try {
        const response = await authenticateUser(credentials)

        if (response && !response.Error) {
          setActiveTab('home')

          await fetchUserData()
        }

        if (response.Error) {
          return {
            errorMessage: response.Error.Message,
            errorCode: response.Error.Code,
            properties: response.Properties,
            success: false,
          }
        }

        if (response.Properties.length) {
          const userProperties = transformResponse(response.Properties)

          // If Custom:User:Id is missing, set it to Content:Id
          if (
            !userProperties['Custom:User:Id'] &&
            userProperties['Content:Id']
          ) {
            userProperties['Custom:User:Id'] = userProperties['Content:Id']
            console.log(
              'Setting Custom:User:Id from Content:Id:',
              userProperties['Content:Id'],
            )
          }

          // Add explicit debug logging
          console.log(
            '🔄 About to check/update LoyaltyMatch registration with user data:',
            {
              userId:
                userProperties['Custom:User:Id'] ||
                userProperties['Content:Id'],
              hasEmail: !!userProperties['Custom:User:Email'],
              email: userProperties['Custom:User:Email'],
            },
          )

          // Log the user ID for verification
          console.log(
            '✅ User authenticated - Custom:User:Id:',
            userProperties['Custom:User:Id'],
          )
          console.log('📋 Full auth response properties:', userProperties)

          // Add this more prominent log just for the UUID
          console.log(
            '🔑 USER UUID FOR LOYALTYMATCH:',
            userProperties['Custom:User:Id'],
          )

          setUserToLocalStorage(userProperties as unknown as UserData)

          // Register with LoyaltyMatch if not already registered
          // Now with enhanced email update functionality
          const loyaltyResult = await registerWithLoyaltyMatch(
            userProperties as unknown as UserData,
          )
          console.log(
            '🔄 LoyaltyMatch registration/update result:',
            loyaltyResult,
          )

          // fetchUserData()
          localStorage.removeItem('provider')
          // setActiveTab('home')
        }

        return { success: true }
      } catch (err) {
        console.log('err:', err)
        return {
          success: false,
          errorCode: 'UNKNOWN',
        }
      } finally {
        setAuthLoading(false)
      }
    },
    [fetchUserData, setActiveTab],
  )

  const registerUser = useCallback(
    async (userData: UserData & AuthSocialData) => {
      setAuthLoading(true)
      try {
        // Register user in your system
        const response = await registerUserAction(userData)

        if (response.Properties && response.Properties.length) {
          const userProperties = transformResponse(response.Properties)
          setUserToLocalStorage(userProperties as unknown as UserData)

          // Register with LoyaltyMatch - now with email support
          await registerWithLoyaltyMatch(userProperties as unknown as UserData)

          fetchUserData()
          localStorage.removeItem('provider')
          setActiveTab('home')
        }

        return { success: true }
      } catch (error) {
        return {
          errorCode: typeof error === 'string' ? error : 'UNKNOWN',
          success: false,
        }
      } finally {
        setAuthLoading(false)
      }
    },
    [fetchUserData, setActiveTab],
  )

  const logout = useCallback(
    (preserveData = false) => {
      console.log('Logging out user:', localStorage.getItem('widgetUserId'))

      // Always remove the token
      localStorage.removeItem('widgetUserToken')

      // // Only remove user data if we're not preserving it
      // if (!preserveData) {
      //   localStorage.removeItem('widgetUserData')
      //   localStorage.removeItem('widgetUserId')
      //   localStorage.removeItem('widgetSessionExpired')
      // } else {
      //   // Mark session as expired if preserving data
      //   localStorage.setItem('widgetSessionExpired', 'true')
      // }

      localStorage.removeItem('widgetUserData')
      localStorage.removeItem('widgetUserId')
      // localStorage.removeItem('widgetSessionExpired')
      localStorage.setItem('widgetSessionExpired', preserveData.toString())

      setUserData(null)
      setSessionExpired(preserveData)
      setActiveTab('home')
    },
    [setActiveTab, setUserData],
  )

  const value = useMemo(
    () => ({
      authLoading,
      registerUser,
      logout,
      login,
      sessionExpired,
      isAuthRequired,
    }),
    [login, logout, registerUser, authLoading, sessionExpired, isAuthRequired],
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
