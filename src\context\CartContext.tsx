import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react'
import { shopifyClient } from '@/lib/shopify'
import { CREATE_CHECKOUT } from '@/queries/shopify-queries'

interface CartItem {
  variantId: string
  quantity: number
  title: string
  price: string
  image?: string
  size?: string | null
  color?: string | null
}

interface CartContextType {
  items: CartItem[]
  addToCart: (item: CartItem) => void
  removeFromCart: (variantId: string) => void
  updateQuantity: (variantId: string, quantity: number) => void
  checkout: () => Promise<void>
  isLoading: boolean
}

const CART_STORAGE_KEY = 'occ-widget-cart-items'

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>(() => {
    // Load cart items from localStorage on initial render
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem(CART_STORAGE_KEY)
      return savedCart ? JSON.parse(savedCart) : []
    }
    return []
  })

  const [isLoading, setIsLoading] = useState(false)

  // Save cart items to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items))
    }
  }, [items])

  const addToCart = (item: CartItem) => {
    setItems((currentItems) => {
      const existingItem = currentItems.find(
        (i) => i.variantId === item.variantId,
      )
      if (existingItem) {
        return currentItems.map((i) =>
          i.variantId === item.variantId
            ? { ...i, quantity: i.quantity + item.quantity }
            : i,
        )
      }
      return [...currentItems, item]
    })
  }

  const removeFromCart = (variantId: string) => {
    setItems((items) => items.filter((item) => item.variantId !== variantId))
  }

  const updateQuantity = (variantId: string, quantity: number) => {
    setItems((currentItems) =>
      currentItems.map((item) =>
        item.variantId === variantId ? { ...item, quantity } : item,
      ),
    )
  }

  const checkout = async () => {
    setIsLoading(true)
    let newWindow: Window | null = null

    try {
      // Log the request for debugging
      console.log('Checkout request:', {
        lines: items.map((item) => ({
          merchandiseId: item.variantId,
          quantity: item.quantity,
        })),
      })

      // Open in a new window/tab instead of redirecting the current page
      newWindow = window.open('', '_blank')

      const response = await shopifyClient.request(CREATE_CHECKOUT, {
        variables: {
          input: {
            lines: items.map((item) => ({
              merchandiseId: item.variantId,
              quantity: item.quantity,
            })),
          },
        },
      })

      console.log('Checkout response:', response)

      // Check if we have a valid checkout URL
      const checkoutUrl = response?.data?.cartCreate?.cart?.checkoutUrl
      if (checkoutUrl && newWindow) {
        newWindow.location.href = checkoutUrl
      } else {
        if (newWindow) newWindow.close()
        throw new Error('No checkout URL received from Shopify')
      }
    } catch (error) {
      console.error('Checkout error details:', error)
      // Handle specific error cases if needed
      if (newWindow) newWindow.close()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <CartContext.Provider
      value={{
        items,
        addToCart,
        removeFromCart,
        updateQuantity,
        checkout,
        isLoading,
      }}
    >
      {children}
    </CartContext.Provider>
  )
}

export const useCart = () => {
  const context = useContext(CartContext)
  if (!context) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
