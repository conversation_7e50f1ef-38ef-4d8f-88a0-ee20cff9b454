import React, { createContext, useContext, useState, useCallback } from 'react'
import { useUser } from './UserContext'
import { LoyaltyMatchService } from '@/lib/loyaltyMatch'

// Define event types we'll track
export enum IncentiveEventType {
  CHAT_MESSAGE_SENT = 'chat_message_sent',
  VIDEO_PLAYED = 'video_played',
}

// Mapping of events to incentive IDs
const EVENT_INCENTIVE_MAP = {
  [IncentiveEventType.CHAT_MESSAGE_SENT]: 7, // Chat Participant incentive
  [IncentiveEventType.VIDEO_PLAYED]: 9, // VOD incentive
}

interface IncentiveTrackerContextType {
  trackEvent: (
    eventType: IncentiveEventType,
    metadata?: Record<string, any>, // Add this parameter
  ) => Promise<void>
  isProcessing: boolean
}

const IncentiveTrackerContext = createContext<
  IncentiveTrackerContextType | undefined
>(undefined)

export function useIncentiveTracker() {
  const context = useContext(IncentiveTrackerContext)
  if (!context) {
    throw new Error(
      'useIncentiveTracker must be used within IncentiveTrackerProvider',
    )
  }
  return context
}

export function IncentiveTrackerProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const { userData } = useUser()
  const userId = userData?.['Custom:User:Id'] || userData?.['Content:Id']
  const [isProcessing, setIsProcessing] = useState(false)

  // Main function to track events and award incentives
  const trackEvent = useCallback(
    async (eventType: IncentiveEventType, metadata?: Record<string, any>) => {
      if (!userId || isProcessing) return

      // Get incentive ID for this event
      const incentiveId = EVENT_INCENTIVE_MAP[eventType]
      if (!incentiveId) return

      try {
        setIsProcessing(true)

        const loyaltyService = new LoyaltyMatchService()
        const url = `${loyaltyService.proxyUrl}/memberpointsactivity`

        const payload = {
          userId,
          incentiveId,
          // Include merchantId if provided in metadata
          ...(metadata?.merchantId && { merchantId: metadata.merchantId }),
          transactionDate: new Date().toISOString().split('T')[0],
          pointsActivityTypeId: 4,
        }

        console.log(
          `Awarding incentive ${incentiveId} for event ${eventType}:`,
          payload,
        )

        const response = await fetch(url, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })

        if (!response.ok) {
          throw new Error(`Failed to award incentive: ${await response.text()}`)
        }

        console.log(
          `Successfully awarded incentive ${incentiveId} for ${eventType}`,
        )
      } catch (err) {
        console.error(`Failed to award incentive for ${eventType}:`, err)
      } finally {
        setIsProcessing(false)
      }
    },
    [userId, isProcessing],
  )

  return (
    <IncentiveTrackerContext.Provider value={{ trackEvent, isProcessing }}>
      {children}
    </IncentiveTrackerContext.Provider>
  )
}
