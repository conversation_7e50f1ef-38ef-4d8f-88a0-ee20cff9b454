import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
  useMemo,
} from 'react'
import { UserData } from '@/types/user'
import { getCustomerById, updateUser } from '@/api/user'
// import { useAppContext } from './AppContext'

interface UserContextType {
  handleUpdateUserData: (data: Partial<UserData>) => void
  isUserAuthenticated: boolean
  fetchUserData: () => void
  userData: UserData | null
  setUserData: React.Dispatch<React.SetStateAction<UserData | null>>
  setIsUserAuthenticated: React.Dispatch<React.SetStateAction<boolean>>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [userData, setUserData] = useState<UserData | null>(null)

  // Add this logging to verify userData and specifically the UUID
  useEffect(() => {
    console.log('📍 UserContext userData:', userData)
    console.log('📍 UserContext UUID:', userData?.['Custom:User:Id'])
    console.log('📍 localStorage UUID:', localStorage.getItem('widgetUserId'))
  }, [userData])

  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false)

  useEffect(() => {
    setIsUserAuthenticated(userData !== null)

    if (userData) {
      console.log('✅ User authenticated! Now redirecting...')
    }
  }, [userData])

  const fetchUserData = useCallback(async () => {
    const userSocialId = localStorage.getItem('widgetUserId') || null
    if (userSocialId) {
      const userDbData = await getCustomerById(userSocialId)

      // Add this fallback to ensure Custom:User:Id is always set
      if (
        userDbData &&
        !userDbData['Custom:User:Id'] &&
        userDbData['Content:Id']
      ) {
        userDbData['Custom:User:Id'] = userDbData['Content:Id']
        console.log(
          'UserContext: Setting Custom:User:Id from Content:Id:',
          userDbData['Content:Id'],
        )
      }

      setUserData(userDbData)
    } else {
      console.log('No user data found')
    }
  }, [])

  useEffect(() => {
    if (!userData) {
      fetchUserData()
    }
  }, [fetchUserData, userData])

  const handleUpdateUserData = useCallback(
    (data: Partial<UserData>) => {
      if (!userData?.['Content:Id']) {
        console.error('User ID not found')
        return
      }

      updateUser(userData?.['Content:Id'], data)
      setUserData((prevData) => {
        const updatedData = { ...prevData, ...data }
        return updatedData as UserData
      })
    },
    [userData],
  )

  const value = useMemo(
    () => ({
      isUserAuthenticated,
      handleUpdateUserData,
      fetchUserData,
      setUserData,
      userData,
      setIsUserAuthenticated,
    }),
    [
      userData,
      handleUpdateUserData,
      fetchUserData,
      isUserAuthenticated,
      setIsUserAuthenticated,
    ],
  )

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>
}

export const useUser = () => {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
