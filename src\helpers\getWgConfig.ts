import { useAppStore } from '../store'

type WGConfig = {
  PaymentType: string
  PrivateKey: string
  PublicKey: string
  QRCodeUrl: string
  WSSUrl: string
}

export default () => {
  const { endpoints } = useAppStore.getState()
  const url = window.location.host.includes('beta.sabby.tv')
    ? endpoints?.CustomWGSTG
    : endpoints?.CustomWGPROD

  const parsed = url ? (JSON.parse(url || '') as WGConfig) : null

  return parsed
}
