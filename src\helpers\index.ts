import { useAppStore } from '@/store'
import { ContentActionType, PropertiesType } from '@/types/app'
import { CarouselSlide } from '@/types/carousel'
import { ContentDetails } from '@/types/content'

export const uuidv4 = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    // eslint-disable-next-line
    var r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8

    return v.toString(16)
  })
}

export const getLanguage = (): string =>
  (navigator.languages.length &&
    navigator.languages.find((lang) => lang.includes('-'))) ||
  navigator.languages[0] ||
  navigator.language ||
  'en'

export const handleDataForCorrectDisplay = (
  array: PropertiesType[],
): { [key: string]: string } => {
  const normalizedObject: { [key: string]: string } = {}
  for (let i = 0; i < array?.length; i++) {
    const key = array[i]?.Name
    normalizedObject[key] = array[i]?.Value
  }

  return normalizedObject
}

export const transformResponse = (
  data: PropertiesType[],
): Record<string, string> => {
  return Object.fromEntries(
    (data || []).map(({ Name, Value }) => [Name, Value]),
  )
}

export const t = (key: string): string => {
  if (!key) return ''

  const locale = localStorage.getItem('locale') || 'en-US'
  const { locales } = useAppStore.getState()

  return locales && locales[locale] && locales[locale][key]
    ? locales[locale][key]
    : key
}

export const actionsParser = (
  data: ContentActionType[],
): Record<string, string> => {
  return Object.fromEntries(
    (data || []).map(({ Action, Url }) => [Action, Url]),
  )
}

export const transformDataToCarousel = (
  data: ContentDetails[],
): CarouselSlide[] => {
  return data
    ? data.map((item) => ({
        id: item['Content:Id'],
        title: item['Content:Title'],
        image:
          item['Customer:Image:SquareUrl'] ||
          item['Customer:Image:LandscapeUrl'] ||
          item['Customer:Image:PortraitUrl'],
      }))
    : []
}
