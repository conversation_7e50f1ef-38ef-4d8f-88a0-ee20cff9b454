import {
  CollectionsType,
  ContentActionType as ContentAction,
  PropertiesType,
} from '../types/app'

type ReturnType = {
  [key: string]: string
}

export const propertiesParser = <T = ReturnType>(
  properties: PropertiesType[] | undefined,
): T => {
  if (!properties) {
    return {} as T
  }

  return properties.reduce((prev, curr) => {
    const collectionName = curr.CollectionName?.split(':')?.join('')
    const name = curr.Name?.split(':')?.join('')

    const prevValue = prev as {
      [key: string]: {
        [key: string]: string
      }
    }

    if (collectionName) {
      let value = {}
      if (name === 'ContentRoleCast') {
        value =
          name in prevValue
            ? {
                [name]: {
                  ...prevValue[name],
                  [collectionName]: curr.Value,
                },
              }
            : {
                [name]: {
                  [collectionName]: curr.Value,
                },
              }
      } else {
        value =
          collectionName in prevValue
            ? {
                [collectionName]: {
                  ...prevValue[collectionName],
                  [name]: curr.Value,
                },
              }
            : {
                [collectionName]: {
                  [name]: curr.Value,
                },
              }
      }

      return {
        ...prev,
        ...value,
      }
    } else if (name in prevValue) {
      return {
        ...prev,
        [name]: prevValue[name] + ',' + curr.Value,
      }
    } else {
      return {
        ...prev,
        [name]: curr.Value,
      }
    }
  }, {} as T)
}

export const valuesParser = (values: { [key: string]: string }): ReturnType =>
  Object.entries(values)?.reduce(
    (prev, [key, value]) => ({
      ...prev,
      [key?.split(':')?.join('')]: value,
    }),
    {},
  )

export const actionsParser = <T = ReturnType>(actions: ContentAction[]): T =>
  actions?.reduce((prev, curr) => {
    return {
      ...prev,
      [curr.Action?.split(/[:\-]/)?.join('')]: curr.Url,
    }
  }, {} as T)

export const contentParser = <T = ReturnType>(
  content: CollectionsType,
  actions = true,
): T => {
  const contentData = {
    ...propertiesParser(content?.Properties ? content.Properties : []),
  }
  if (actions)
    contentData.Actions = actionsParser(
      content?.Actions as unknown as ContentAction[],
    )

  return contentData as T
}

export default contentParser

export const base62 = {
  charset:
    '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.split(''),
  encode: (integer: number) => {
    let int = integer
    if (int === 0) {
      return 0
    }
    let s: string[] = []
    while (int > 0) {
      s = [base62.charset[int % 62], ...s]
      int = Math.floor(int / 62)
    }

    return s.join('')
  },
  decode: (chars: string) =>
    chars
      .split('')
      .reverse()
      .reduce(
        (prev, curr, i) => prev + base62.charset.indexOf(curr) * 62 ** i,
        0,
      ),
}
