import { PropertiesType } from '@/types/app'
import { AuthUserData, UserData } from '@/types/user'
import { handleDataForCorrectDisplay } from '.'

export const getUserId = (): string | null => {
  const userId = localStorage.getItem('widgetUserId')

  return userId ? userId : null
}

export const getUserToken = (): string | null => {
  const userToken = localStorage.getItem('widgetUserToken')

  return userToken ? userToken : null
}

export const getLocalStorageUserData = (): UserData | null => {
  const user = localStorage.getItem('widgetUserData')

  return user ? JSON.parse(user) : null
}

export const setStorageLocales = (locales: {
  [key: string]: { [key: string]: string }
}): void => {
  localStorage.setItem('Locales', JSON.stringify(locales))
}

export const getStorageLocales = (): {
  [key: string]: { [key: string]: string }
} | null => {
  const locales = localStorage.getItem('Locales')
  if (locales) return JSON.parse(locales)
  else return null
}

export const updateUserToken = (data: PropertiesType[]): void => {
  const user = localStorage.getItem('UserData')
  if (user) {
    const oldData = JSON.parse(user) as AuthUserData
    const newData = handleDataForCorrectDisplay(data) as AuthUserData
    const updated = Object.assign(oldData, newData)
    localStorage.setItem('UserData', JSON.stringify(updated))
  }
}

export const getUser = (): UserData | null => {
  const user = localStorage.getItem('UserData')

  return user ? JSON.parse(user) : null
}

export const removeUser = (): void => localStorage.removeItem('UserData')
