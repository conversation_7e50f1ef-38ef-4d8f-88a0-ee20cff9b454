import { fetcher } from '@/api'
import { actionsParser, transformResponse } from '@/helpers'
import { ContentCollection, FetchReturn } from '@/types/app'
import { ContentDetails } from '@/types/content'
import { useMemo } from 'react'
import { KeyedMutator } from 'swr'
import useSwrImmutable from 'swr/immutable'

type ReturnData = FetchReturn & {
  content: ContentDetails | undefined
  refreshData: KeyedMutator<ContentCollection>
}

export default (id: string | undefined | null): ReturnData => {
  const cacheUrl = id
    ? 'https://cdn.oc-club.community/api/v6/content/${contentId}/entity.json'.replace(
        '${contentId}',
        id,
      )
    : null

  const { data, error, isLoading, mutate } = useSwrImmutable<ContentCollection>(
    cacheUrl ? cacheUrl : null,
    { fetcher },
  )

  const content =
    data &&
    data.Properties &&
    ({
      ...(transformResponse(data.Properties) as unknown as ContentDetails),
      Actions: actionsParser(data.Actions),
    } as ContentDetails)

  return {
    isLoading: isLoading,
    error: error
      ? error instanceof SyntaxError
        ? error.message
        : 'something went wrong'
      : undefined,
    content,
    refreshData: mutate,
  }
}

export const useFormattedDate = (dateStr: string | undefined) => {
  return useMemo(() => {
    if (!dateStr) return ''

    const date = new Date(dateStr)

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
    }

    return date.toLocaleDateString('en-US', options)
  }, [dateStr])
}
export const useFormattedDuration = (durationInMilliseconds?: string) => {
  return useMemo(() => {
    if (!durationInMilliseconds) return null

    const totalSeconds = Math.floor(parseInt(durationInMilliseconds, 10) / 1000)

    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)

    const formattedTime = [
      hours > 0 ? `${hours}h` : '',
      minutes > 0 ? `${minutes}m` : '',
    ].join(' ')

    return formattedTime
  }, [durationInMilliseconds])
}
