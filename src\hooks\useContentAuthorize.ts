import useSwrImmutable from 'swr/immutable'
import { KeyedMutator } from 'swr'
import { contentAuthorize } from '@/api/auth'
import { ContentDetails } from '@/types/content'
import { transformResponse } from '@/helpers'
import { ContentAuthorizeType } from '@/types/video'

type ReturnData = {
  isLoading: boolean
  error: string
  data: ContentAuthorizeType[] | undefined
  errorCode: string
  refetch: KeyedMutator<
    ContentAuthorizeType[] | [{ error: string; code: string }]
  >
}

const contentAuthorizeFetcher = async <T>(
  contents: ContentDetails[],
): Promise<[{ error: string; code: string }] | T> => {
  const response = await Promise.all(
    contents.map((c) => contentAuthorize(c['Content:Id'])),
  )

  return response?.map((r) => {
    if (!r.Error) {
      return {
        ...(transformResponse(r.Properties) as unknown as ContentAuthorizeType),
      }
    }

    const stripeSubscriptions = r.ChildItems.map((item) =>
      transformResponse(item.Properties),
    )

    return {
      error: r.Error.Message,
      code: r.Error.Code,
      stripeSubscriptions,
    }
  }) as T
}

export default (contents: ContentDetails[] | []): ReturnData => {
  const { data, error, isLoading, mutate } = useSwrImmutable<
    ContentAuthorizeType[] | [{ error: string; code: string }]
  >(contents.length ? contents : null, { fetcher: contentAuthorizeFetcher })

  const customError =
    data && data.length && 'error' in data[0]
      ? data[0].code === 'ERROR_UNAUTHORIZED'
        ? data[0].code
        : data[0].error
      : null

  return {
    isLoading,
    error: customError || error?.message,
    data: data as ContentAuthorizeType[],
    errorCode: error?.Code || (data && data[0]?.code),
    refetch: mutate,
  }
}
