import { useEffect, useRef } from 'react'
import * as Shaka from 'shaka-player/dist/shaka-player.compiled'
import { fetcher } from '../api'
import { getUser } from '../helpers/storage'
import { useAppStore } from '../store'
import { ContentDetails } from '@/types/content'

const sendTime = 30

export default (
  contentData: ContentDetails | undefined,
  shakaInstance: Shaka.Player | null,
  sessionId?: string,
) => {
  const playTime = useRef(0)
  const intervalId = useRef<NodeJS.Timeout | null>(null)
  const content = useRef<ContentDetails | null>(null)
  const sessionIdRef = useRef('')

  useEffect(() => {
    if (contentData && !content?.current) content.current = contentData
  }, [contentData])

  useEffect(() => {
    if (sessionId && !sessionIdRef.current) sessionIdRef.current = sessionId
  }, [sessionId])

  const sendClientReport = async (playTime: number): Promise<void> => {
    if (window.location.href.includes('localhost')) return

    if (playTime <= 0) return

    try {
      const video = shakaInstance?.getMediaElement()
      if (!video) return
      const user = getUser()
      const { endpoints } = useAppStore.getState()
      const url =
        endpoints?.['Custom:Application:Reporting'] ||
        'https://twe.oc-club.community/clientreport'

      const body = [
        {
          Name: 'Type',
          Value: 'ContentConsumptionContract',
        },
        {
          Name: 'ContentRef',
          Value: content?.current?.['Content:Id'] || '',
        },
        {
          Name: 'UserRef',
          Value: user?.['Custom:User:Id'] || '',
        },
        {
          Name: 'ContentToken',
          Value: content?.current?.['Content:Token'] || '',
        },
        {
          Name: 'CollectionRef',
          Value: '',
        },
        {
          Name: 'UserToken',
          Value: user?.['Custom:User:Token'] || '',
        },
        {
          Name: 'ProjectRef',
          Value: '6561c7ca-f013-4eb4-aa09-921a80f77e05',
        },
        {
          Name: 'Genre',
          Value: content?.current?.['Content:Genre'] || '',
        },
        {
          Name: 'Category',
          Value: content?.current?.['Content:Category'] || '',
        },
        {
          Name: 'Date',
          Value: new Date().toISOString(),
        },
        {
          Name: 'SessionId',
          Value: sessionIdRef.current,
        },
        {
          Name: 'ObjecType',
          Value: 'Content',
        },
        {
          Name: 'CatalogRef',
          Value: '',
        },
        {
          Name: 'Position',
          Value: video?.currentTime,
        },
        {
          Name: 'Duration',
          Value: video?.duration,
        },
        {
          Name: 'Speed',
          Value: video?.playbackRate,
        },
        {
          Name: 'TimeSpent',
          Value: playTime,
        },
      ]

      await fetcher(url + 'clientreport', {
        method: 'post',
        body: JSON.stringify({
          Properties: body,
        }),
      })
    } catch (err) {
      console.log('err:', err)
    }
  }

  const trackPlayTime = () => {
    intervalId.current = setInterval(() => {
      playTime.current += 1
      if (playTime.current >= sendTime) {
        sendClientReport(playTime.current)
        playTime.current = 0
      }
    }, 1000)
  }

  const onPlay = () => {
    console.log('play')
    // updateControls({ playing: true })
    sendClientReport(playTime.current)

    if (!intervalId.current) {
      trackPlayTime()
    }
  }

  const onPause = () => {
    console.log('pause')
    sendClientReport(playTime.current)
    // updateControls({ playing: false })

    if (intervalId.current) clearInterval(intervalId.current)
    intervalId.current = null
  }

  const onSeek = () => {
    console.log('seek')
    sendClientReport(playTime.current)

    if (intervalId.current) clearInterval(intervalId.current)
    intervalId.current = null
  }

  const onEnded = () => {
    console.log('end')
    sendClientReport(playTime.current)

    if (intervalId.current) clearInterval(intervalId.current)
    intervalId.current = null
    playTime.current = 0
  }

  useEffect(() => {
    return () => {
      sendClientReport(playTime.current)
      if (intervalId.current) clearInterval(intervalId.current)
      intervalId.current = null
      playTime.current = 0
    }
  }, [])

  return {
    onPause,
    onPlay,
    onSeek,
    onEnded,
  }
}
