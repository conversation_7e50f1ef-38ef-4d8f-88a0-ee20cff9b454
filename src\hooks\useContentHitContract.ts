import useSwrImmutable from 'swr/immutable'
import { fetcher } from '../api'
import useUserLicense from './useUserLicense'
import { getUser } from '../helpers/storage'
import { useAppStore } from '../store'
import { License } from '../types/subscription'
import { ContentDetails } from '@/types/content'

export default (contentData?: ContentDetails, sessionId?: string): void => {
  const { license, isLoading } = useUserLicense()

  const { error } = useSwrImmutable(
    contentData?.['Content:Id'] && !isLoading /* && sessionId */
      ? contentData?.['Content:Id']
      : null,
    () => clientReportFetcher(contentData, license, sessionId),
  )

  if (error) console.log('error:', error)
}

const clientReportFetcher = async (
  content: ContentDetails | undefined,
  license?: License,
  sessionId?: string,
): Promise<void> => {
  if (window.location.href.includes('localhost')) return

  try {
    const user = getUser()
    const { endpoints } = useAppStore.getState()
    const url =
      endpoints?.['Custom:Application:Reporting'] ||
      'https://twe.oc-club.community/'

    const body = [
      {
        Name: 'Type',
        Value: 'ContentHitContract',
      },
      {
        Name: 'ContentRef',
        Value: content?.['Content:Id'],
      },
      {
        Name: 'UserRef',
        Value: user?.['Custom:User:Id'] || '',
      },
      {
        Name: 'ContentToken',
        Value: content?.['Content:Token'],
      },
      {
        Name: 'CollectionRef',
        Value: '',
      },
      {
        Name: 'UserToken',
        Value: user?.['Custom:User:Token'] || '',
      },
      {
        Name: 'ProjectRef',
        Value: '6561c7ca-f013-4eb4-aa09-921a80f77e05',
      },
      {
        Name: 'Genre',
        Value: content?.['Content:Genre'] || '',
      },
      {
        Name: 'Category',
        Value: content?.['Content:Category'] || '',
      },
      {
        Name: 'Date',
        Value: new Date().toISOString(),
      },
      {
        Name: 'SessionId',
        Value: sessionId || '',
      },
      {
        Name: 'ProductId',
        Value: license?.ProductId,
      },
      {
        Name: 'ObjecType',
        Value: 'Content',
      },
      {
        Name: 'CatalogRef',
        Value: '',
      },
    ]

    await fetcher(url + '/clientreport', {
      method: 'post',
      body: JSON.stringify({
        Properties: body,
      }),
    })
  } catch (err) {
    console.log('err:', err)
  }
}
