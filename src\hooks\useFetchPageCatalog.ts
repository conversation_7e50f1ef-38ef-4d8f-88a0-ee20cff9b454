import { transformResponse } from '@/helpers'
import { CollectionsType, PropertiesType } from '@/types/app'
import { ContentDetails } from '@/types/content'
import { useState, useEffect } from 'react'
import { useTokenRefresh } from '@/hooks/useTokenRefresh'

interface PageCatalogResult {
  data:
    | {
        carouselTitle: string | undefined
        data: ContentDetails[]
        catalogId: string
      }[]
    | undefined
  loading: boolean
  error: string | null
}

export const useFetchPageCatalog = (
  url: string,
  isHomeRender?: boolean,
): PageCatalogResult => {
  const { ensureValidTokenForApiCall } = useTokenRefresh()

  const [data, setData] = useState<PageCatalogResult['data']>(undefined)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchJson = async (url: string) => {
      return ensureValidTokenForApiCall(async () => {
        try {
          const response = await fetch(url)
          if (!response.ok)
            throw new Error(`HTTP error! status: ${response.status}`)
          return response.json()
        } catch (error) {
          console.error(`Error fetching data from ${url}:`, error)
          return null
        }
      }, isHomeRender)
    }

    const fetchData = async () => {
      try {
        setLoading(true)
        const initialResponse = await fetchJson(url)
        if (!initialResponse) throw new Error('Failed to fetch initial data')

        const childItems = initialResponse.ChildItems || []

        const dataFromActionsUrls = await Promise.all(
          childItems.map((item: CollectionsType) =>
            item.Actions?.[0]?.Url ? fetchJson(item.Actions[0].Url) : null,
          ),
        )

        const getChildrenItemsFromChildItems = (
          await Promise.all(
            dataFromActionsUrls.map(async (data) => {
              if (!data?.Actions?.[0]?.Url) return null

              const fetchedData = await fetchJson(data.Actions[0].Url)
              if (!fetchedData) return null

              const title = data.Properties?.find(
                (property: PropertiesType) => property.Name === 'Content:Title',
              )?.Value

              const catalogId = data.Properties?.find(
                (property: PropertiesType) => property.Name === 'Content:Id',
              )?.Value

              return {
                data: fetchedData.ChildItems || [],
                catalogId,
                title,
              }
            }),
          )
        ).filter(Boolean)

        const finalData = (
          await Promise.all(
            getChildrenItemsFromChildItems.map(async (childItem) => {
              if (!childItem?.data) return null

              const transformedData = await Promise.all(
                childItem.data.map(async (nestedItem: CollectionsType) => {
                  if (!nestedItem.Actions?.[0]?.Url) return null

                  const nestedData = await fetchJson(nestedItem.Actions[0].Url)
                  return nestedData
                    ? (transformResponse(
                        nestedData.Properties,
                      ) as ContentDetails)
                    : null
                }),
              )

              return {
                data: transformedData.filter(Boolean) as ContentDetails[],
                carouselTitle: childItem.title,
                catalogId: childItem.catalogId,
              }
            }),
          )
        ).filter(Boolean)

        if (isMounted)
          setData(
            finalData.filter(
              (
                item,
              ): item is {
                carouselTitle: string | undefined
                data: ContentDetails[]
                catalogId: string
              } => item !== null,
            ),
          )
      } catch (err) {
        if (isMounted) setError((err as Error).message)
      } finally {
        if (isMounted) setLoading(false)
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, [url])

  return { data, loading, error }
}
