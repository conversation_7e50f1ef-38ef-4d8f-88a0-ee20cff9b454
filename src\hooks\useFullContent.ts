import { useMemo } from 'react'
import useContent from './useContent'
import { ContentDetails } from '@/types/content'
import useContentAuthorize from './useContentAuthorize'

export type FullContent = ContentDetails & {
  stripeSubscriptions?: Record<string, any>
}

export const useFullContent = (virtualEventId: string | undefined | null) => {
  const {
    content,
    isLoading: contentLoading,
    error,
    refreshData,
  } = useContent(virtualEventId)

  const {
    isLoading: authLoading,
    data: authorizedContentData,
    error: authError,
    refetch,
  } = useContentAuthorize(content ? [content as ContentDetails] : [])

  const fullContent: FullContent | undefined = useMemo(() => {
    if (!content) return undefined

    return {
      ...content,
      stripeSubscriptions: authorizedContentData?.[0]?.stripeSubscriptions,
    }
  }, [content, authorizedContentData])

  return {
    content: fullContent,
    isLoading: contentLoading || authLoading,
    error: error || authError,
    refetch,
    refreshData,
  }
}
