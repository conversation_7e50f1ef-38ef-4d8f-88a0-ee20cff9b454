import useSwrImmutable from 'swr/immutable'
import { ContentCollection, FetchReturn, Locales } from '@/types/app'
import { fetcher } from '@/api'
import { useAppStore } from '@/store'

type ReturnData = FetchReturn & {
  data: Locales | undefined
}

const checkLocalesExpired = (url: string): string | null => {
  const now = Date.now()
  const localesExpiry = localStorage.getItem('LocalesExpiry')

  if (!localesExpiry) return url

  const expiryTime = Number(localesExpiry)
  if (isNaN(expiryTime) || expiryTime <= 0) return url

  // Only return URL to fetch if expired - don't delete anything yet
  return now > expiryTime ? url : null
}

export default (): ReturnData => {
  const { endpoints } = useAppStore.getState()

  const { data, isLoading, error } = useSwrImmutable<ContentCollection>(
    endpoints?.['Custom:Application:LocalizationUrl']
      ? checkLocalesExpired(endpoints?.['Custom:Application:LocalizationUrl'])
      : null,
    { fetcher },
  )

  // Parse localization the same way
  const parsedLocalization = data?.ChildItems?.length
    ? data?.ChildItems?.map((child) => {
        const locale = child?.Properties[0]?.CollectionName
        if (!locale) return

        return {
          [locale]: child?.Properties?.reduce(
            (prev, next) => ({
              ...prev,
              [next?.Name]: next?.Value,
            }),
            {},
          ),
        }
      })?.reduce((prev, next) => ({ ...prev, ...next }), {})
    : undefined

  return {
    data:
      parsedLocalization && Object.keys(parsedLocalization)?.length
        ? parsedLocalization
        : undefined,
    isLoading,
    error: error
      ? error instanceof SyntaxError
        ? error.message
        : 'something went wrong'
      : undefined,
  }
}
