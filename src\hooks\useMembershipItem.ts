import { useState, useEffect, useRef } from 'react'
import { MembershipPlan } from '@/types/subscription'
import { CollectionsType } from '@/types/app'
import { transformResponse } from '@/helpers'
import { FullContent } from './useFullContent'

type UseMembershipPlansReturn = {
  memberships: MembershipPlan[] | undefined
  isLoading: boolean
  error: string
}

const useMembershipItem = ({
  content,
}: {
  content: FullContent | undefined
}): UseMembershipPlansReturn => {
  const [memberships, setAllAvailableMemberships] = useState<
    MembershipPlan[] | undefined
  >(undefined)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>('')

  const hasFetchedRef = useRef(false)

  useEffect(() => {
    if (
      hasFetchedRef.current ||
      !content?.stripeSubscriptions ||
      !content.stripeSubscriptions.length
    ) {
      return
    }

    hasFetchedRef.current = true

    const fetchMembershipPlans = async () => {
      try {
        setIsLoading(true)

        if (content?.stripeSubscriptions?.length) {
          const results = await Promise.all(
            content?.stripeSubscriptions
              .filter((sub: any) => !!sub['Content:Url'])
              .map(async (sub: any) => {
                try {
                  const response = await fetch(sub['Content:Url'])
                  if (!response.ok)
                    throw new Error('Failed to fetch subscription')

                  const data: CollectionsType = await response.json()
                  return transformResponse(data.Properties) as MembershipPlan
                } catch (err) {
                  console.warn(
                    '❌ Error fetching one of the stripe subscriptions:',
                    err,
                  )
                  return null
                }
              }),
          )

          const filteredResults = results.filter(Boolean) as MembershipPlan[]
          setAllAvailableMemberships(filteredResults)
          setError('')
        }
      } catch (error: unknown) {
        setAllAvailableMemberships(undefined)
        setError(
          error instanceof Error ? error.message : 'An unknown error occurred',
        )
      } finally {
        setIsLoading(false)
      }
    }

    fetchMembershipPlans()
  }, [content?.stripeSubscriptions])

  return { memberships, isLoading, error }
}

export default useMembershipItem
