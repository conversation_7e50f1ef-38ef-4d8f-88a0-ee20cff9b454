import useSwrImmutable from 'swr/immutable'
import {
  attachPaymentMethodToCustomer,
  createInvoice,
  createInvoiceItem,
  createPaymentIntent,
  createStripeCustomer,
  detachPaymentMethod,
  finalizeInvoice,
  getUserStripeAccountById,
  payInvoice,
  updateExternalIds,
  updateStripeCustomer,
} from '@/api/subscription'
import { useCallback, useState } from 'react'
import { PaymentMethod } from '@stripe/stripe-js'
import {
  CustomerAccount,
  MembershipPlan,
  SUBSCRIPTIONS_PLAN_TYPES,
} from '@/types/subscription'
import { UserData } from '@/types/user'
import { getLocalStorageUserData } from '@/helpers/storage'
import { fetcher } from '@/api'
import { CollectionsType } from '@/types/app'

type Return = {
  clientSecret: string | null | undefined
  stripeCustomer: CustomerAccount | undefined
  makePayment: (paymentMethod: PaymentMethod, name?: string) => Promise<void>
  error: Error | undefined
}

export default (user: UserData | undefined, plan: MembershipPlan): Return => {
  const [error, setError] = useState<Error | undefined>(undefined)
  const { data: paymentIntents } = useSwrImmutable(
    'https://api.stripe.com/v1/payment_intents',
    () => createPaymentIntent(),
  )

  const { data: stripeCustomer } = useSwrImmutable(
    user?.Actions?.['Subscription:Retrieve:ExternalIds']
      ? user.Actions?.['Subscription:Retrieve:ExternalIds']
      : null,
    stripeCustomerFetcher,
  )

  const oneTimePayment = useCallback(
    async (
      method: PaymentMethod,
      customer: CustomerAccount,
      productCode: string,
    ): Promise<void> => {
      try {
        await createInvoiceItem(productCode, customer.id)
        const invoice = await createInvoice(customer.id, method.id)
        const finalizedInvoice = await finalizeInvoice(invoice.id)

        if (finalizedInvoice.id) {
          await payInvoice(finalizedInvoice.id)
          console.log('Transaction is completed. Thank you!')
        }
      } catch (err) {
        console.log('err:', err)
        console.log('error', 'Something went wrong')
      }
    },
    [],
  )

  const createSubscription = async (
    сustomer: CustomerAccount,
    productCode: string,
  ) => {
    const myHeaders = new Headers()
    myHeaders.append(
      'Authorization',
      'Bearer sk_test_51RecxOIhAqXGxPPc847bIowjlZ24HHUaiHuquVytu3PkFl0bpN4D6y8f3NMjW0wTsSSVt1rD5tgZHJRnF0o2W0xI00jmvDtFaB',
    )
    myHeaders.append('Content-Type', 'application/x-www-form-urlencoded')
    const user = getLocalStorageUserData()
    const urlencoded = new URLSearchParams()
    urlencoded.append('customer', сustomer.id)
    urlencoded.append('items[0][price]', productCode)
    if (user != null) {
      urlencoded.append('metadata[customerId]', user['Custom:User:Id'])
    }
    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: urlencoded,
      redirect: 'follow',
    }

    // @ts-expect-error: The fetch function may not match the expected TypeScript type, but it is handled correctly in runtime.
    return fetch('https://api.stripe.com/v1/subscriptions', requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.error) {
          return
        }
        console.log('Transaction is completed. Thank you!')
        return result
      })
      .catch((error) => {
        setError(error)
      })
  }

  const makePayment = useCallback(
    async (paymentMethod: PaymentMethod, name?: string) => {
      try {
        if (!stripeCustomer || stripeCustomer?.deleted) {
          const customer = await createStripeCustomer(paymentMethod?.id, name)
          if (
            customer &&
            user &&
            user.Actions?.['Subscription:Update:ExternalIds']
          ) {
            let newCustomer = customer
            if (!customer.invoice_settings.default_payment_method) {
              const newPaymentMethod = await attachPaymentMethodToCustomer(
                paymentMethod.id,
                customer.id,
              )
              newCustomer = await updateStripeCustomer(
                customer.id,
                newPaymentMethod.id,
              )
            }

            await updateExternalIds(
              user.Actions?.['Subscription:Update:ExternalIds'],
              newCustomer.id,
            )

            if (
              plan['Product:Type'] === SUBSCRIPTIONS_PLAN_TYPES.SUBSCRIPTION
            ) {
              await createSubscription(newCustomer, plan['Product:Code'])
            } else {
              await oneTimePayment(
                paymentMethod,
                newCustomer,
                plan['Product:Code'],
              )
            }
          }
        } else {
          const {
            invoice_settings: { default_payment_method },
          } = stripeCustomer
          if (default_payment_method) {
            await detachPaymentMethod(default_payment_method)
          }
          const newPaymentMethod = await attachPaymentMethodToCustomer(
            paymentMethod.id,
            stripeCustomer.id,
          )

          const updatedCustomer = await updateStripeCustomer(
            stripeCustomer.id,
            newPaymentMethod.id,
          )

          if (plan['Product:Type'] === SUBSCRIPTIONS_PLAN_TYPES.SUBSCRIPTION) {
            await createSubscription(updatedCustomer, plan['Product:Code'])
          } else {
            await oneTimePayment(
              newPaymentMethod,
              updatedCustomer,
              plan['Product:Code'],
            )
          }
        }
      } catch (err) {
        console.log('err:', err)
        console.log(
          'error',
          'Transaction failed. Please review your credit card information',
        )
      }
    },
    [oneTimePayment, plan['Product:Code'], stripeCustomer, user],
  )

  return {
    clientSecret: paymentIntents?.client_secret,
    stripeCustomer,
    makePayment,
    error: error,
  }
}

const stripeCustomerFetcher = async (
  url: string,
): Promise<CustomerAccount | undefined> => {
  try {
    const response = await fetcher<CollectionsType>(url, {})
    if (response?.Properties?.length) {
      const externalId = response.Properties.find(
        (prop) => prop.Name === 'Custom:ExternalIds:Stripe',
      )
      if (externalId) {
        const stripeAccount = await getUserStripeAccountById(externalId.Value)

        return stripeAccount
      }
    }
  } catch (error) {
    console.log('error: stripe customer', error)
  }
}
