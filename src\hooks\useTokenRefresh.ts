import { useState, useEffect, useCallback } from 'react'
import dayjs from 'dayjs'
import { jwtDecode } from 'jwt-decode'
import { getLocalStorageUserData, updateUserToken } from '../helpers/storage'
import { useUser } from '@/context/UserContext'

// Constants for token refresh
const TOKEN_REFRESH_THRESHOLD_SECONDS = 300 // Refresh if less than 5 minutes remaining
const TOKEN_CHECK_INTERVAL_MS = 60000 // Check token every minute

export function useTokenRefresh() {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [tokenExpiresAt, setTokenExpiresAt] = useState<number | null>(null)
  const [tokenIsValid, setTokenIsValid] = useState<boolean>(true)

  const { setIsUserAuthenticated } = useUser()

  // Function to check if token is valid and get expiry time
  const checkTokenValidity = useCallback((): {
    isValid: boolean
    expiresAt: number | null
    expiresInSeconds: number | null
  } => {
    const user = getLocalStorageUserData()
    if (!user || !user['Custom:User:Token']) {
      return { isValid: false, expiresAt: null, expiresInSeconds: null }
    }

    try {
      const userToken = user['Custom:User:Token']
      const decoded = jwtDecode<{ exp: number }>(userToken)
      const now = dayjs().unix()
      const expiresInSeconds = decoded.exp - now

      // Valid if more than threshold seconds remaining
      const isValid = expiresInSeconds > TOKEN_REFRESH_THRESHOLD_SECONDS
      return {
        isValid,
        expiresAt: decoded.exp,
        expiresInSeconds,
      }
    } catch (e) {
      console.error('❌ Failed to decode token:', e)
      return { isValid: false, expiresAt: null, expiresInSeconds: null }
    }
  }, [])

  const renewToken = async (
    userToken: string,
    refreshToken?: string,
  ): Promise<{ success: boolean; newToken?: string }> => {
    if (!refreshToken) return { success: false }

    try {
      // Refresh if less than 5 minutes remain
      const renewUrl = 'https://bgj.oc-club.community/Interop/RenewToken'

      const newTokens = await fetch(renewUrl, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'Accept-Charset': 'utf-8',
        },
        method: 'POST',
        body: JSON.stringify([
          { Name: 'Custom:User:Token', Value: userToken },
          { Name: 'Custom:Refresh:Token', Value: refreshToken },
          {
            Name: 'Custom:Device:UniqueId',
            Value: localStorage.getItem('Custom:Device:UniqueId'),
          },
        ]),
      })

      const tokens = await newTokens.json()

      if (newTokens.ok && !tokens.Error) {
        updateUserToken(tokens.Properties)
        const newToken = tokens?.Properties?.find(
          (prop: { Name: string; Value: string }) =>
            prop.Name === 'Custom:User:Token',
        )?.Value

        return { success: true, newToken }
      }

      return { success: false }
    } catch (error) {
      console.error('Token refresh error:', error)
      return { success: false }
    }
  }

  // Refresh token implementation
  const refreshToken = useCallback(
    async (force = false): Promise<boolean> => {
      const user = getLocalStorageUserData()
      if (!user) {
        console.log('🔄 useTokenRefresh: No user data found in local storage')
        setTokenIsValid(false)
        return false
      }

      const userToken = user['Custom:User:Token']
      const refreshToken = user['Custom:Refresh:Token']

      if (!userToken || !refreshToken) {
        console.log('🔄 useTokenRefresh: Missing token or refresh token')
        setTokenIsValid(false)
        return false
      }

      try {
        // Skip refresh if token is still valid and force is false
        if (!force) {
          const { isValid, expiresAt, expiresInSeconds } = checkTokenValidity()
          setTokenExpiresAt(expiresAt)
          setTokenIsValid(isValid)

          if (isValid) {
            console.log(
              `✅ useTokenRefresh: Token still valid (expires in ${expiresInSeconds}s)`,
            )
            return true
          }
        }

        setIsRefreshing(true)
        setError(null)
        console.log(
          '🔄 useTokenRefresh: Token needs refresh, attempting refresh',
        )

        const renewUrl = 'https://bgj.oc-club.community/Interop/RenewToken'

        const newTokens = await fetch(renewUrl, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'Accept-Charset': 'utf-8',
          },
          method: 'POST',
          body: JSON.stringify([
            {
              Name: 'Custom:User:Token',
              Value: userToken,
            },
            {
              Name: 'Custom:Refresh:Token',
              Value: refreshToken,
            },
            {
              Name: 'Custom:Device:UniqueId',
              Value: localStorage.getItem('Custom:Device:UniqueId'),
            },
          ]),
        })

        const tokens = await newTokens.json()

        if (newTokens.ok && !tokens.Error) {
          console.log('✅ useTokenRefresh: Token refresh successful')
          updateUserToken(tokens.Properties)
          localStorage.removeItem('authRequired')

          // Update token expiry information after refresh
          const updatedUser = getLocalStorageUserData()
          if (updatedUser && updatedUser['Custom:User:Token']) {
            try {
              const newToken = updatedUser['Custom:User:Token']
              const decoded = jwtDecode<{ exp: number }>(newToken)
              setTokenExpiresAt(decoded.exp)
              setTokenIsValid(true)

              const now = dayjs().unix()
              console.log(
                `🔐 New token will expire at: ${new Date(
                  decoded.exp * 1000,
                ).toISOString()}`,
              )
              console.log(
                `🕒 New token expires in: ${Math.floor(
                  (decoded.exp - now) / 3600,
                )}h ${Math.floor(((decoded.exp - now) % 3600) / 60)}m`,
              )
            } catch (e) {
              console.error('❌ Failed to decode new token:', e)
            }
          }

          setIsRefreshing(false)
          return true
        } else {
          console.error(
            '❌ useTokenRefresh: Token refresh failed',
            tokens?.Error || 'Unknown error',
          )
          setError(new Error('Failed to refresh token'))
          setTokenIsValid(false)
          setIsRefreshing(false)
          setIsUserAuthenticated(false)
          return false
        }
      } catch (error) {
        console.error('❌ useTokenRefresh: Token refresh error:', error)
        setError(
          error instanceof Error
            ? error
            : new Error('Unknown error during token refresh'),
        )
        setTokenIsValid(false)
        setIsRefreshing(false)
        setIsUserAuthenticated(false)
        return false
      }
    },
    [checkTokenValidity],
  )

  // Automatically check token validity periodically
  useEffect(() => {
    // Check token on component mount
    const checkAndRefreshIfNeeded = async () => {
      const { isValid, expiresAt, expiresInSeconds } = checkTokenValidity()
      setTokenExpiresAt(expiresAt)
      setTokenIsValid(isValid)

      if (
        !isValid &&
        expiresInSeconds !== null &&
        expiresInSeconds < TOKEN_REFRESH_THRESHOLD_SECONDS
      ) {
        console.log('🔄 Auto refresh: Token expiring soon, refreshing...')
        await refreshToken()
      }
    }

    checkAndRefreshIfNeeded()

    // Set up interval to check token validity periodically
    const intervalId = setInterval(
      checkAndRefreshIfNeeded,
      TOKEN_CHECK_INTERVAL_MS,
    )

    return () => {
      clearInterval(intervalId)
    }
  }, [checkTokenValidity, refreshToken])

  // Provide an explicit method to check if we need to refresh before an API call
  const ensureValidToken = useCallback(async (): Promise<boolean> => {
    const { isValid } = checkTokenValidity()
    if (!isValid) {
      return refreshToken()
    }
    return true
  }, [checkTokenValidity, refreshToken])

  const ensureValidTokenForApiCall = useCallback(
    async (
      apiCall: () => Promise<any>,
      isHomeRender?: boolean,
    ): Promise<any> => {
      if (isHomeRender) return apiCall()

      const { isValid } = checkTokenValidity()
      if (!isValid) {
        const refreshed = await refreshToken()
        if (!refreshed) {
          throw new Error('Token refresh failed. Cannot proceed with API call.')
        }
      }

      return apiCall()
    },
    [checkTokenValidity, refreshToken],
  )

  return {
    refreshToken,
    ensureValidToken, // New method to use before API calls
    ensureValidTokenForApiCall,
    renewToken,
    isRefreshing,
    error,
    tokenExpiresAt,
    isTokenValid: tokenIsValid,
  }
}
