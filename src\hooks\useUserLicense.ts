import { fetcher } from '@/api'
import contentParser from '../helpers/propertiesParser'
import { useAppStore } from '@/store'
import { CollectionsType } from '@/types/app'
import { License } from '@/types/subscription'
import useSwrImmutable from 'swr/immutable'

export default () => {
  const { endpoints } = useAppStore.getState()

  // const url = endpoints?.CustomApplicationCustomerSubscriptionGetUrl || ''
  const url =
    endpoints?.['Custom:Application:Customer:Subscription:Get'] ||
    'https://twe.oc-club.community/license'

  const { data, error, isLoading } = useSwrImmutable<CollectionsType>(
    url ? url : null,
    {
      fetcher,
    },
  )

  const license = data?.ChildItems?.[0]?.Properties?.length
    ? contentParser<License>(data?.ChildItems?.[0])
    : undefined

  return { license, isLoading, error }
}
