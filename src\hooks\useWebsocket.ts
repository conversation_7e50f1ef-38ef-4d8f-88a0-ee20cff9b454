import { useEffect, useRef } from 'react'
import useWebSocket, { ReadyState } from 'react-use-websocket'
import { getLocalStorageUserData } from '../helpers/storage'
import { UserData } from '@/types/user'
import { useAppContext } from '@/context/AppContext'
import { EventBus } from '@/helpers/EventBus'
import { useTokenRefresh } from './useTokenRefresh'

type WSMessage = {
  action: string
}

const useWebsocket = (): void => {
  const socketUrl = 'wss://704m5zwpr4.execute-api.us-east-1.amazonaws.com/prod/'

  const { setIsContentPaymentUpdated } = useAppContext()
  const { renewToken } = useTokenRefresh()

  const isMountedRef = useRef(true)

  useEffect(() => {
    isMountedRef.current = true
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const { readyState, sendJsonMessage, lastJsonMessage } = useWebSocket(
    socketUrl,
    {
      retryOnError: true,
      shouldReconnect: () => isMountedRef.current,
    },
  )

  useEffect(() => {
    const user = getLocalStorageUserData()
    const data = {
      accessToken: user?.['Custom:User:Token'],
    }
    if (readyState === ReadyState.OPEN && user?.['Custom:User:Token']) {
      console.log('open connection')
      sendJsonMessage(data)
    }

    if (readyState === ReadyState.OPEN && !user?.['Custom:User:Token']) {
      console.log('open connection')
      sendJsonMessage({
        accessToken: '',
      })
    }
  }, [readyState])

  const setUserToLocalStorage = (data: UserData): void => {
    // Fix: Use optional chaining or nullish coalescing to handle undefined values
    localStorage.setItem('widgetUserToken', data['Custom:User:Token'] || '')
    localStorage.setItem('widgetUserId', data['Custom:User:Id'] || '')
    localStorage.setItem('widgetUserData', JSON.stringify(data))

    // Optionally store expiration separately for easier access
    if (data['Custom:Token:Expiration']) {
      localStorage.setItem('tokenExpiration', data['Custom:Token:Expiration'])
    }

    setIsContentPaymentUpdated(true)
  }

  const newTokens = async (): Promise<void> => {
    try {
      const user = getLocalStorageUserData()

      const userToken = user?.['Custom:User:Token']
      const refreshToken = user?.['Custom:Refresh:Token']

      if (userToken) {
        const refreshResult = await renewToken(userToken, refreshToken)

        if (refreshResult.newToken) {
          const userProperties = {
            ...user,
            ['Custom:User:Token']: refreshResult.newToken,
          }

          setUserToLocalStorage(userProperties as unknown as UserData)
        }
      }

      EventBus.$emit('closeSubscriptionModal')
    } catch (error) {
      console.log('error:', error)
    } finally {
      EventBus.$emit('membershipRefresh')
    }
  }

  useEffect(() => {
    if (lastJsonMessage) {
      console.log('WS Message from server', lastJsonMessage)

      if ((lastJsonMessage as WSMessage)?.action === 'RenewToken') {
        newTokens()
      }
      if ((lastJsonMessage as WSMessage)?.action === 'GetSubscription') {
        EventBus.$emit('membershipRefresh')
      }
    }
  }, [lastJsonMessage])
}

export default useWebsocket
