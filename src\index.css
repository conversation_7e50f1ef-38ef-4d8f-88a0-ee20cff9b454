@tailwind base;
@tailwind components;
@tailwind utilities;

img {
  max-width: 100%;
  height: inherit;
}

.foreign-blur {
  display: block;

  @media (max-width: 768px) {
    display: none;
  }
}

@layer base {
  h1 {
    @apply text-3xl font-semibold text-foreground leading-[48px];
  }
  h2 {
    @apply text-3xl;
  }
  h3 {
    @apply text-2xl;
  }
  h4 {
    @apply text-xl leading-6 tracking-[-0.4px];
  }

  :root {
    --accent: 188 86% 53%;
    --accent-3: 188 86% 53%;
    --accent-5: 25 95% 53%;
    --accent-foreground: 210 20% 98%;
    --background: 240 10% 4%;
    --border: 215 28% 17%;
    --card: 240 5% 34%;
    --card-foreground: 210 20% 98%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;
    --foreground: 0 0% 98%;
    --foreground-inverse: 240 6% 10%;
    --input: 240 4% 16%;
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 20% 98%;
    --primary: 188 86% 53%;
    --primary-foreground: 240 6% 10%;
    --radius: 0.5rem;
    --ring: 263 70% 50%;
    --secondary: 240 4% 16%;
    --secondary-foreground: 210 20% 98%;
    --sidebar-hub-background: 240 4% 16%;
    --sidebar-widget-background: 240 10% 4%;
  }

  .dark {
    --accent-3: 188 86% 53%;
    --accent-5: 25 95% 53%;
    --accent-foreground: 210 20% 98%;
    --background: 240 10% 4%;
    --border: 215 28% 17%;
    --card: 240 5% 34%;
    --card-foreground: 210 20% 98%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;
    --foreground: 0 0% 98%;
    --input: 240 4% 16%;
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 20% 98%;
    --primary-foreground: 240 6% 10%;
    --ring: 263 70% 50%;
    --secondary: 240 4% 16%;
    --secondary-foreground: 210 20% 98%;
  }

  html {
    font-family: 'Inter' !important;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply text-foreground;
  }
}

.hexagon {
  width: 100px;
  height: 115px;
  background: hsl(var(--primary) / 0.2);
  position: relative;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hexagon-inner {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: hsl(var(--background));
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.hexagon-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
}

p p {
  margin-bottom: 1rem;
}

iframe {
  width: 100%;
}

.list-disc {
  list-style-type: disc;
  padding-left: 1.5rem;
}
