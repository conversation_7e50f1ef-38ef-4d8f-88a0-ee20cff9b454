import type { Artist, ArtistConfig } from '@/types/artist'

export const DEFAULT_SLUG = 'bonnie-raitt'

export const loadArtistConfigBySlug = async (
  slug: string,
): Promise<ArtistConfig> => {
  try {
    const configModule = await import(`@/configs/${slug}.json`)
    return configModule.default
  } catch (e) {
    console.error(`Config not found for slug: ${slug}, using default`)
    const fallback = await import(`@/configs/${DEFAULT_SLUG}.json`)
    return fallback.default
  }
}

export const getArtistConfig = async (
  artistId: string,
  formattedFeaturedArtistData: Artist[],
): Promise<ArtistConfig> => {
  const found = formattedFeaturedArtistData.find(
    (artist) => artist.consoleArtistId === artistId,
  )

  const slug = found?.id || DEFAULT_SLUG
  return await loadArtistConfigBySlug(slug)
}

type CSSProperty = 'color' | 'background' | 'backgroundColor'

export function getStyledProps(
  value: string,
  property: CSSProperty = 'color',
): { className?: string; style?: React.CSSProperties } {
  if (!value) return {}
  return value.startsWith('#')
    ? { style: { [property]: value } }
    : { className: value }
}

export function getTextColorStyle(
  colorValue?: string,
): React.CSSProperties | undefined {
  if (!colorValue) return undefined
  return colorValue.startsWith('#') ? { color: colorValue } : undefined
}

export function getTailwindColorClass(
  colorValue: string = '',
  type: 'bg' | 'text' | 'border' = 'bg',
): string {
  if (!colorValue) return ''

  if (colorValue.startsWith('#')) {
    return `${type}-[${colorValue}]`
  }

  return colorValue
}
