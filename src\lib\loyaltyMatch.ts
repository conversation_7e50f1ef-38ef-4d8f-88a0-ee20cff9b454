/* eslint-disable @typescript-eslint/no-unused-vars */
import config from './config.json'
import { useTokenRefresh } from '@/hooks/useTokenRefresh'
import { LoyaltyTransaction } from '@/types/loyalty'

export enum LoyaltyActionType {
  PURCHASE = 'purchase',
  CHAT_ENGAGEMENT = 'chat_engagement',
  CONTENT_VIEW = 'content_view',
  PROFILE_COMPLETE = 'profile_complete',
  SOCIAL_SHARE = 'social_share',
}

interface LoyaltyPoints {
  type: LoyaltyActionType
  points: number
  metadata?: Record<string, any>
}

export class LoyaltyMatchService {
  private readonly apiUrl: string
  private readonly apiKey: string
  readonly proxyUrl: string // Make this public so it can be accessed
  private readonly endpoints: Record<string, string>

  constructor() {
    // this.apiUrl = import.meta.env.DEV
    //   ? 'http://localhost:3000/api/loyalty' // Local development
    //   : 'https://widget.oc-club.community/api/loyalty' // Production (relative URL)
    // this.apiKey = config.VITE_LOYALTY_MATCH_API_KEY
    // this.proxyUrl = import.meta.env.DEV
    //   ? 'http://localhost:3000/api/loyalty' // Local development
    //   : 'https://widget.oc-club.community/api/loyalty' // Production (relative URL)

    this.apiUrl = 'https://widget.oc-club.community/api/loyalty'
    this.apiKey = config.VITE_LOYALTY_MATCH_API_KEY
    this.proxyUrl = 'https://widget.oc-club.community/api/loyalty'

    // Create a complete endpoints object with default values for missing endpoints
    this.endpoints = {
      ...config.VITE_LOYALTY_MATCH_ENDPOINTS,
      AWARD_POINTS:
        config.VITE_LOYALTY_MATCH_ENDPOINTS.AWARD_POINTS ||
        'memberpointsactivity',
      GET_BALANCE:
        config.VITE_LOYALTY_MATCH_ENDPOINTS.GET_BALANCE ||
        'currentpoints/userId',
      GET_TRANSACTIONS:
        config.VITE_LOYALTY_MATCH_ENDPOINTS.GET_TRANSACTIONS || 'transactions',
    }
  }

  async makeRequest(
    endpoint: string,
    data: any,
    method: string = 'GET',
  ): Promise<any> {
    const { ensureValidTokenForApiCall } = useTokenRefresh()

    return ensureValidTokenForApiCall(async () => {
      const params = new URLSearchParams({
        format: 'json',
        odpass: this.apiKey,
        ...(method === 'GET' ? data : {}),
      }).toString()

      const url = `${this.apiUrl}${endpoint}?${params}`
      console.log('Making request to:', url)

      const options: RequestInit = {
        method,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        credentials: 'same-origin',
      }

      if (method !== 'GET') {
        options.body = JSON.stringify(data)
      }

      const response = await fetch(url, options)
      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error:', errorText)
        throw new Error(`LoyaltyMatch API request failed: ${errorText}`)
      }

      return response.json()
    })
  }

  async awardPoints(
    userId: string,
    action: LoyaltyPoints,
  ): Promise<LoyaltyTransaction> {
    return this.makeRequest(
      this.endpoints.AWARD_POINTS,
      {
        userId,
        actionType: action.type,
        points: action.points,
        metadata: action.metadata,
      },
      'POST',
    )
  }

  async getPointsBalance(userId: string): Promise<number> {
    const { ensureValidTokenForApiCall } = useTokenRefresh()

    return ensureValidTokenForApiCall(async () => {
      const url = `${this.proxyUrl}/currentpoints/userId/${userId}`

      console.log('Fetching points balance from:', url)
      const response = await fetch(url)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to fetch points: ${errorText}`)
      }

      const data = await response.json()
      // Return the appropriate field based on response structure
      return data.total || data.totalCurrentPoints || data.points || 0
    })
  }

  async redeemPoints(
    userId: string,
    rewardId: string,
    points: number,
  ): Promise<LoyaltyTransaction> {
    return this.makeRequest(
      this.endpoints.REDEEM_POINTS,
      {
        userId,
        rewardId,
        points,
      },
      'POST',
    )
  }

  async getTransactions(userId: string): Promise<LoyaltyTransaction[]> {
    // Change this to use the memberpointsactivities endpoint instead of transactions
    const url = `${this.proxyUrl}/memberpointsactivities/userId/${userId}`

    console.log('Fetching transactions from:', url)
    const response = await fetch(url)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to fetch transactions: ${errorText}`)
    }

    const data = await response.json()
    // Process the response based on actual data structure
    const activities = data.activities || data.apiMemberPointsActivities || []

    // Map to the LoyaltyTransaction format
    return activities.map((activity: any) => ({
      id: activity.memberPointsActivityId?.toString() || `tx-${Date.now()}`,
      timestamp: new Date(activity.transactionDate || Date.now()),
      points: Math.abs(activity.points || 0),
      type: activity.points >= 0 ? 'earn' : 'redeem',
      actionId: activity.transactionEnglishDescription || 'Points transaction',
    }))
  }

  async registerUser(userData: {
    userId: string
    email?: string
    firstName?: string
    lastName?: string
  }): Promise<any> {
    const url = `${this.proxyUrl}/register`
    // Update to include email in the request body
    const body = {
      userId: userData.userId,
      emailAddress: userData.email, // Include email in the request
    }

    // Log full request details
    console.log('LoyaltyMatch registration details:', {
      fullUrl: url,
      proxyUrl: this.proxyUrl,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: body,
      userData: userData,
    })

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    })

    // Log response status and headers
    console.log('LoyaltyMatch registration response status:', response.status)
    console.log('LoyaltyMatch registration response headers:', {
      contentType: response.headers.get('Content-Type'),
      statusText: response.statusText,
    })

    const responseText = await response.text()
    console.log('LoyaltyMatch registration raw response:', responseText)

    if (!response.ok) {
      console.error(
        'LoyaltyMatch registration failed with status:',
        response.status,
      )
      throw new Error(`Registration failed: ${responseText}`)
    }

    try {
      return JSON.parse(responseText)
    } catch (e) {
      console.error('Failed to parse LoyaltyMatch response as JSON:', e)
      throw new Error(`Invalid JSON response: ${responseText}`)
    }
  }

  async updateMember(userData: {
    userId: string
    email?: string
    apiMemberAccount?: {
      twitterHandle?: string
      facebookHandle?: string
      instagramHandle?: string
      emailAddress?: string
    }
  }): Promise<any> {
    const url = `${this.proxyUrl}/updatemember`

    // Prepare the apiMemberAccount object
    const apiMemberAccount: Record<string, any> = {
      ...(userData.apiMemberAccount || {}),
    }

    // If email is provided directly, add it to apiMemberAccount
    if (userData.email) {
      apiMemberAccount.emailAddress = userData.email
    }

    const body = {
      userId: userData.userId,
      apiMemberAccount,
    }

    console.log('LoyaltyMatch member update call:', {
      url,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
      proxyUrl: this.proxyUrl,
      baseUrl: import.meta.env.VITE_LOYALTY_PROXY_URL || 'not set',
    })

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    })

    const responseText = await response.text()
    console.log('LoyaltyMatch member update response:', responseText)

    if (!response.ok) {
      throw new Error(`Member update failed: ${responseText}`)
    }

    try {
      return responseText ? JSON.parse(responseText) : { success: true }
    } catch (e) {
      console.error('Failed to parse LoyaltyMatch response as JSON:', e)
      return { success: true, message: 'Member updated' }
    }
  }

  async testConnection(): Promise<{ message: string }> {
    const { ensureValidTokenForApiCall } = useTokenRefresh()

    return ensureValidTokenForApiCall(async () => {
      const response = await fetch(`${this.proxyUrl}/test`)
      if (!response.ok) {
        throw new Error('Connection test failed')
      }
      return response.json()
    })
  }
}

export const loyaltyPoints = {
  purchase: (amount: number): LoyaltyPoints => ({
    type: LoyaltyActionType.PURCHASE,
    points: Math.floor(amount * 10), // $1 = 10 points
    metadata: { amount },
  }),
  chatEngagement: (): LoyaltyPoints => ({
    type: LoyaltyActionType.CHAT_ENGAGEMENT,
    points: 5, // 5 points per chat message
  }),
  contentView: (): LoyaltyPoints => ({
    type: LoyaltyActionType.CONTENT_VIEW,
    points: 20, // 20 points per content view
  }),
  profileComplete: (): LoyaltyPoints => ({
    type: LoyaltyActionType.PROFILE_COMPLETE,
    points: 100, // 100 points for completing profile
  }),
  socialShare: (): LoyaltyPoints => ({
    type: LoyaltyActionType.SOCIAL_SHARE,
    points: 50, // 50 points per share
  }),
}
