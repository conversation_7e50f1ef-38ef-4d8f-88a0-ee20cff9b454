import config from './config.json'

const API_BASE_URL = config.VITE_SPARX_API_URL

// Keep only the interfaces needed for your poll management system
export interface PollChoice {
  position: number
  label: string
  _id?: string
}

export interface ApiPollQuestion {
  question: string
  showIn: number
  answerTime: number
  showResultTime: number
  choices: PollChoice[]
  _id: string
}

export interface PollData {
  _id: string
  type: string
  artist_id: string
  polls: ApiPollQuestion[]
  start_time: string
  status?: number
  current_question?: any
  createdAt?: string
  updatedAt?: string
  __v?: number
}

// Update the function with better debugging

export const getActivePollByArtist = async (
  artistId: string,
): Promise<PollData | null> => {
  try {
    console.log('🔍 polls.ts - Fetching active poll for artistId:', artistId)

    const response = await fetch(
      `${API_BASE_URL}/polls/active/artist/${artistId}`,
    )

    console.log('📊 polls.ts - Response status:', response.status)

    if (!response.ok) {
      if (response.status === 404) {
        console.log('📭 polls.ts - No active poll found (404)')
        return null // No active poll found
      }
      throw new Error(`Failed to fetch active poll: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('📥 polls.ts - Raw API response:', data)

    return data
  } catch (error) {
    console.error('💥 polls.ts - Error fetching active poll:', error)
    throw error
  }
}
