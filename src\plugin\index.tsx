import { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import App from '../App'
import '../index.css'

interface OCC2WidgetProps {
  position?: 'left' | 'right'
  artistId?: string
  isOpen?: boolean
}

export const OCC2Widget = ({
  position = 'right',
  artistId,
  isOpen = false,
}: OCC2WidgetProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(isOpen)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [mountNode, setMountNode] = useState<HTMLElement | null>(null)
  const [emotionCache, setEmotionCache] = useState<ReturnType<
    typeof createCache
  > | null>(null)
  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768)

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    setIsSidebarOpen(isOpen)
  }, [isOpen])

  useEffect(() => {
    const iframe = iframeRef.current
    if (!iframe) return

    const onLoad = () => {
      const doc = iframe.contentDocument
      if (!doc) return

      doc.open()
      doc.write('<!DOCTYPE html><html><head></head><body></body></html>')
      doc.close()

      const style = doc.createElement('style')
      style.innerHTML = `
        html, body {
          background-color: transparent !important;
          margin: 0;
          padding: 0;
        }
      `
      doc.head.appendChild(style)

      const mount = doc.createElement('div')
      doc.body.appendChild(mount)

      const fontLink = doc.createElement('link')
      fontLink.rel = 'stylesheet'
      fontLink.href =
        'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap'
      doc.head.appendChild(fontLink)

      const styleLink = doc.createElement('link')
      styleLink.rel = 'stylesheet'
      styleLink.href = 'https://unpkg.com/occ2-widget@latest/dist/index.css'
      doc.head.appendChild(styleLink)

      const cache = createCache({
        key: 'iframe',
        container: doc.head,
      })

      setMountNode(mount)
      setEmotionCache(cache)
    }

    if (iframe.contentDocument?.readyState === 'complete') {
      onLoad()
    } else {
      iframe.addEventListener('load', onLoad)
      return () => iframe.removeEventListener('load', onLoad)
    }
  }, [isSidebarOpen])

  const toggleSidebar = () => {
    setIsSidebarOpen((prev) => !prev)
  }

  return (
    <div className="fixed z-[999999] bg-transparent">
      <iframe
        ref={iframeRef}
        title="OCC2 Widget"
        style={{
          width: !isSidebarOpen ? '40px' : isMobile ? '100%' : '90%',
          height: '100vh',
          position: 'fixed',
          top: 0,
          [position]: 0,
          border: 'none',
          zIndex: isSidebarOpen ? 999999 : 1,
          backgroundColor: 'transparent',
        }}
      />

      {mountNode &&
        emotionCache &&
        createPortal(
          <CacheProvider value={emotionCache}>
            <App
              toggleSidebar={toggleSidebar}
              isSidebarOpen={isSidebarOpen}
              artistId={artistId}
            />
          </CacheProvider>,
          mountNode,
        )}
    </div>
  )
}

export default OCC2Widget
