import { gql } from '@apollo/client'

export const GET_ARTIST_CONTEXT = gql`
  query BonnieWidgetOptions {
    allBonnieRaittWidget {
      edges {
        node {
          bonnieRaittWidgetACF {
            featuredArtistId
            featuredPostId
            leapId
            memberText
            newsId
            shopifyId
          }
        }
      }
    }
  }
`

//this gets news from the wp graphql api, should only be 5 at a time, ignores the featured category
export const GET_HOME_NEWS = gql`
  query GetNews {
    posts(first: 5, where: { categoryNotIn: "12" }) {
      edges {
        node {
          date
          slug
          title
          uri
          content
        }
      }
    }
  }
`

// this gets the featured post from the wp graphql api, should only be one at a time
export const GET_FEATURED_POST = gql`
  query GetFeaturedPost {
    posts(first: 1, where: { categoryName: "featured" }) {
      edges {
        node {
          date
          slug
          title
          uri
          content
          featuredImage {
            node {
              mediaItemUrl
            }
          }
        }
      }
    }
  }
`

// this gets the featured artists from the wp graphql api
export const GET_FEATURED_ARTISTS = gql`
  query GetFeaturedArtists($first: Int = 1000) {
    featuredArtists(first: $first) {
      edges {
        node {
          featuredImage {
            node {
              mediaItemUrl
            }
          }
          title
          featuredArtistIDACF {
            featuredArtistId
            leapId
            loyaltyMatchMerchantId
            consoleArtistId
            memberText
            shopifyId
            shopifyFeaturedCollectionId
            featuredPostId
            newsId
            bannerImage {
              node {
                mediaItemUrl
              }
            }
          }
        }
      }
    }
  }
`
//this is the query for news on the artist page, it pulls in artistId and gets news articles that match that category in WordPress
export const GET_ARTIST_NEWS = gql`
  query GetArtistNews($artistId: String!) {
    posts(where: { categoryName: $artistId }) {
      edges {
        node {
          id
          content
          date
          title
          featuredImage {
            node {
              mediaItemUrl
            }
          }
        }
      }
    }
  }
`
