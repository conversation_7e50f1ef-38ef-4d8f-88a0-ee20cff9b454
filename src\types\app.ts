export type CollectionsType = {
  Actions: ActionsType[]
  ChildItems: CollectionsType[]
  Properties: PropertiesType[]
  Error?: {
    Code: string
    Message: string
  }
}

export type ActionsType = {
  ActionGroup: number
  EndPointUrl: string
  HttpMethod: string
  ActionType: number
  Text: string
  Url: string
}

export type FetchReturn = {
  error: string | undefined
  isLoading: boolean
}

export type ContentCollection = {
  Actions: ContentActionType[]
  ChildItems: ContentCollection[]
  Properties: PropertiesType[]
  Error?: {
    Code: string
    Message: string
  }
}

export type PropertiesType = {
  CollectionName?: string
  Name: string
  Value: string
}

export type CreateContent = {
  workflowId: string
  originalFileName: string
  originalLanguage: string
  localizations: {
    [key: string]: {
      name: string
    }
  }
  originalTitle: string
  type: string
  exhibitionWindow: {
    '--': {
      availableFrom: string
      availableUntil: string
    }
  }
  duration?: number
  publicUrl: string
  objectUrl: string
  allowMinting: boolean
  allowRemix: boolean
  allowComments: boolean
  published?: boolean
  id?: string
  properties?: {
    [key: string]: string | number
  }
}

export type ContentActionType = {
  HttpMethod: string
  Action: string
  Index: number
  Url: string
}

export type Endpoints = {
  'Custom:Application:ImageProcessing:Url': string
  'Custom:Setting:Home:Featured:Artist': string
  'Custom:Settings:SKU:VirtualTickets': string
  'Custom:Application:LocalizationUrl': string
  'Custom:Setting:Home:Featured:Event': string
  'Custom:Setting:Home:Featured:Tour': string
  'Custom:Settings:SKU:Subscriptions': string
  'Custom:Application:RenewTokenUrl': string
  'Custom:Application:Reporting': string
  'Custom:Application:Customer:Subscription:Get': string
  'Custom:Setting:Artists:All': string
  'Custom:Setting:Exclusive': string
  'Custom:Setting:Artists': string
  'Custom:Setting:Paywall': string
  'Custom:Setting:Live': string
  CustomWGPROD: string
  CustomWGSTG: string
}

export type Locales = {
  [key: string]: {
    [key: string]: string
  }
}
