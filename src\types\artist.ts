import { Tab } from './navigation'

export interface FeaturedArtist {
  node: {
    featuredImage: {
      node: {
        mediaItemUrl: string
      }
    }
    title: string
    featuredArtistIDACF: {
      featuredArtistId: string
      leapId?: string
      loyaltyMatchMerchantId?: string
      consoleArtistId?: string
      memberText?: string
      shopifyId?: string
      shopifyFeaturedCollectionId?: string
      newsId?: string // Add newsId field
      featuredPostId?: string // Add featuredPostId field
      bannerImage?: {
        node: {
          mediaItemUrl: string
        }
      }
    }
  }
}

export interface TourDate {
  location: string
  venue: string
  date: string
}

export interface Artist {
  id: string
  image: string
  title: string
  leapId?: string
  memberText?: string
  shopifyId?: string
  shopifyFeaturedCollectionId?: string
  consoleArtistId?: string
  loyaltyMatchMerchantId?: string
  bannerImage?: string
  newsId?: string
  featuredPostId?: string
  popularSongs?: string[]
  tourDates?: TourDate[]
  bio?: string
}

export interface ConfigMenuProps {
  tab: Tab
  label: string
  Icon: React.FC<React.SVGProps<SVGSVGElement>>
}

export interface ArtistConfig {
  id: string
  name: string
  slug: string
  colors: string | null
  menu: string | null
  merchantId?: string
}

export interface ArtistColorsConfig {
  hoverColor: string
  fontColor: string
  secondaryBackgroundColor: string
  backgroundColor: string
  secondaryText: string
  font: string
  logo: string
}
