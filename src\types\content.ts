export type Content = {
  relatedContentIds?: string[]
  longDescription?: string
  description: string
  videoUrl: string
  poster?: string
  tags?: string[]
  title: string
  date: string
  slug: string
  id: string
}

export type ContentDetails = {
  'Globalization:en-US:Content:Description': string
  'Globalization:en-US:Content:Info': string
  'Globalization:en-US:Content:Title': string
  'Product:Price:Currency': string
  'Content:Landscape:Url': string
  'Content:ReleaseDate': string
  'Product:Price:Amount': string
  'Content:Url': string
  'Content:SquareImage:Url': string
  'Content:Navigation:Url': string
  'Custom:User:Nickname': string
  'Custom:User:Avatar': string
  'Content:Id': string
  'Content:Type': string
  'Content:Title': string
  'Content:Thumbnail:Url': string
  'Content:CreatedDate': string
  'Content:ReleaseDate:TimeZone': string
  'Content:SubType': string
  'Custom:User:UserName': string
  'Content:Token': string
  'Content:Genre': string
  'Content:Category': string
  'Customer:Image:CircleUrl': string
  'Customer:Image:PortraitUrl': string
  'Customer:Image:SquareUrl': string
  'Customer:Service:ExclusiveUrl': string
  'Customer:Service:ExclusiveUrlPhotos': string
  'Customer:Subscription:Promo': string
  'Customer:Services:WP:FeaturedNews:ID': string
  'Customer:Membership:Id': string
  'Content:Info': string
  'Content:PlaybackSessionId': string
  'Customer:Services:Shopify:Collection:ID': string
  'Customer:Services:LEAP:ID': string
  'Content:Description': string
  'Customer:Services:WP:News:ID': string
  'Customer:Service:LiveUrl': string
  'Customer:Image:LandscapeUrl': string
  'Custom:SocialIds:Password': string
  'Customer:Settings:Notification:Email': string
  'Custom:User:Email': string
  'Custom:User:Name': string
  'Custom:User:Phone': string
  'Content:Kind': string
  'Custom:User:Birthday': string
  'Content:Label:Location': string
}
