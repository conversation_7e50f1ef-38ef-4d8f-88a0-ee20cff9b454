export interface Volume {
  mutedVolume: number
  value: number
}

export type VttType = {
  duration: string
  from: string
  imgUrl: string
  to: string
}

export type ContentAuthorizeType = {
  stripeSubscriptions?: any
  'Content:Duration': string
  'Content:Url': string
  error?: string
  code?: string
}
export type ControlStates = {
  playing: boolean
  muted: boolean
  volume: number
  speed: number
  fullscreen?: boolean
}
